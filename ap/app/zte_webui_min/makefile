include $(zte_app_mak)

all:
	echo "zte_webui compile do nothing"

clean:
	echo "zte_webui clean do nothing"

romfs:
	@mkdir -p $(ROOTFS_DIR)/etc_ro/web
	@cp -afvp $(APP_DIR)/zte_webui_min/*   $(ROOTFS_DIR)/etc_ro/web
	find . -type f -name '*.js' | xargs -n1 -I {} java -jar $(YUICOMPRESSOR)  {} -o  $(ROOTFS_DIR)/etc_ro/web/{}
	find . -type f -name '*.css' | xargs -n1 -I {} java -jar $(YUICOMPRESSOR)  {} -o  $(ROOTFS_DIR)/etc_ro/web/{}
	find . -type f -name '*.html' | xargs -n1 -I {} sed -i 's/^[ \t]*//g' $(ROOTFS_DIR)/etc_ro/web/{}
	find . -type f -name '*.html' | xargs -n1 -I {} sed -i 's/[ \t]*$$//g' $(ROOTFS_DIR)/etc_ro/web/{}
	find . -type f -name '*.html' | xargs -n1 -I {} sed -i ":a;N;s/\r//g;ta" $(ROOTFS_DIR)/etc_ro/web/{}
	find . -type f -name '*.html' | xargs -n1 -I {} sed -i ":a;N;s/\n//g;ta" $(ROOTFS_DIR)/etc_ro/web/{}
ifneq ($(CONFIG_WIFI_MODULE), aic8800)
	-rm -v $(ROOTFS_DIR)/etc_ro/web/subpg/wifi_ap_station.html
endif
ifeq ($(CONFIG_WEBUI_TYPE),CPE)
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_cpe.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
ifeq ($(CONFIG_WEBUI_TYPE),CPE_SW)	
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_cpe_sw.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif

ifeq ($(PRJ_IS_MIN), yes)
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_min.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
ifeq ($(CONFIG_WIFI_MODULE), xr819)
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_xr819.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
ifeq ($(CONFIG_WIFI_MODULE), ssv6x5x)
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_ssv6x5x.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
ifeq ($(CONFIG_WIFI_MODULE), aic8800)
ifeq ($(CONFIG_WIFI_SUB_MODULE), d40)
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_aic8800d40i.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
else
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_aic8800.js $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
ifeq ($(CONFIG_WIFI_SINGLEAP), no)
	-sed -i 's/WIFI_SLEEP_SUPPORT:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	-sed -i 's/HAS_BATTERY:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	#zhumouren -sed -i 's/TURN_OFF_SUPPORT:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
else
	-sed -i 's/AP_STATION_SUPPORT:true,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	-sed -i 's/HAS_MULTI_SSID:true,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js	
endif
endif
endif

ifneq ($(CONFIG_USE_WEBUI_SECURITY),yes)
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/3rd/crypto-js.js
	-cp -v $(ROOTFS_DIR)/etc_ro/web/js/ext/crypto-js-null.js $(ROOTFS_DIR)/etc_ro/web/js/3rd/crypto-js.js
	-sed -i 's/PASSWORD_ENCODE:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
ifeq ($(USE_FOTA),yes)
ifneq ($(QRZL_UE),yes)
	-sed -i 's/UPGRADE_TYPE:"NONE",//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	-sed -i 's/HAS_FOTA:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
	-sed -i 's/HAS_UPDATE_CHECK:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
endif
ifeq ($(CONFIG_MMI_LCD),yes)
	-sed -i 's/WIFI_SUPPORT_QR_CODE:false,//' $(ROOTFS_DIR)/etc_ro/web/js/ext/set.js
endif
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/crypto-js-null.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_min.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_xr819.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_ssv6x5x.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_aic8800.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_aic8800d40i.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_cpe.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/js/ext/set_cpe_sw.js
	-rm -v $(ROOTFS_DIR)/etc_ro/web/makefile
ifeq ($(CONFIG_USE_WEBUI_ZIP),yes)
	(cd $(ROOTFS_DIR)/etc_ro && zip -r web.zip web)
	(cd $(ROOTFS_DIR)/etc_ro && rm -rfv web)
endif

#ifeq ($(USE_OEM_FS_QRZL_OTA),yes)
#	@rm -rvf $(PRJ_BIN_DIR)/../../fs/normal/oem/etc/web
#	@mv $(ROOTFS_DIR)/etc_ro/web $(PRJ_BIN_DIR)/../../fs/normal/oem/etc
#endif

