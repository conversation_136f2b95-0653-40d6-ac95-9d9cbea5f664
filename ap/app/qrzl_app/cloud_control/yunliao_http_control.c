#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>


#include "yunliao_http_control.h"
#include "../common_utils/cjson.h"
#include "../common_utils/http_client.h"
#include "../qrzl_utils.h"
#include "curl/curl.h"
#include "softap_api.h"
#include "nv_api.h"

#define APN_FIELD_LEN   65
#define APN_SMALL_LEN   32
#define APN_BUF_LEN     512

/* 请求间隔时间 */
static int request_interval_time = 0;

/* 开始上报标识 */
static int first_report_flag = 0;

/* 云控制上报地址 */
char http_report_request_url[HTTP_REQUEST_MAX] = {0};


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static void response_handler(cJSON* root);

static void init_config()
{
    // 正式入口：
    // http://opend.xn-auto.cn/iotAdap/api/common/device/upload
    // 测试入口：
    // http://opend-test.xn-auto.cn/iotAdap/api/common/device/upload

    // 获取上报地址
    int res1 = cfg_get_item("qrzl_cloud_http_path", http_report_request_url, sizeof(http_report_request_url));
    if (res1 != 0) {
        qrzl_log("获取上报地址失败!");
    }

    // 获取上报间隔
    char interval_time[5] = {0};
    int res2 = cfg_get_item("qrzl_cloud_request_interval_time", interval_time, sizeof(interval_time));
    if (res2 != 0) {
        qrzl_log("获取上报间隔失败，使用默认时间");
        request_interval_time = 300; // 默认300秒
    } else {
        request_interval_time = atoi(interval_time);
    }


}

// 构造apn字符串
static void build_apn_string(char *out, size_t out_size) {
    // --- 基础字段 ---
    char apn_config_name[APN_FIELD_LEN] = {0};
    cfg_get_item("m_profile_name", apn_config_name, sizeof(apn_config_name));
    remove_spaces(apn_config_name);

    char apn_username[APN_FIELD_LEN] = {0};
    cfg_get_item("ppp_username", apn_username, sizeof(apn_username));

    char apn_password[APN_FIELD_LEN] = {0};
    cfg_get_item("ppp_passtmp", apn_password, sizeof(apn_password));

    char apn_login_number[APN_SMALL_LEN] = {0};
    cfg_get_item("wan_dial", apn_login_number, sizeof(apn_login_number));

    char apn_apn[APN_SMALL_LEN] = {0};
    cfg_get_item("wan_apn", apn_apn, sizeof(apn_apn));

    char apn_pdp_type[APN_SMALL_LEN] = {0};
    cfg_get_item("pdp_type", apn_pdp_type, sizeof(apn_pdp_type));

    char apn_auth_type[APN_SMALL_LEN] = {0};
    cfg_get_item("ppp_auth_mode", apn_auth_type, sizeof(apn_auth_type));

    // --- 拼接 APN 信息 ---
    char apn[APN_BUF_LEN] = {0};
    snprintf(apn, sizeof(apn),
             "MCCMNC:%s%s;"
             "ConfigFileName:%s;"
             "UserName:%s;"
             "Password:%s;"
             "LoginNumber:%s;"
             "APN:%s;"
             "PDPType:%s;"
             "AuthType:%s;",
             g_qrzl_device_dynamic_data.mcc,
             g_qrzl_device_dynamic_data.mnc,
             apn_config_name,
             apn_username,
             apn_password,
             apn_login_number,
             apn_apn,
             apn_pdp_type,
             apn_auth_type);

    remove_spaces(apn);

    // --- URL Encode ---
    url_encode(apn, out);
}

/**
 * 设备信息上报
 */
static void device_info_report()
{
    // 更新动态数据
    update_device_dynamic_data();

    char http_post_body[HTTP_REQUEST_MAX] = {0};
    char http_response[HTTP_RESPONSE_MAX] = {0};

    char amountFlow_str[30] = {0};
    char time_str[15] = {0};
    char wifi_ssid_encoded[128] = {0};
    char wifi_key_encoded[258] = {0};
    char conn_cnt_str[5] = {0};
    char remain_pwr_str[3] = {0};
    char wifi_status_str[4] = {0};
    char wifi_hide_str[4] = {0};
    char up_speed_bps_str[30] = {0};
    char down_speed_bps_str[30] = {0};
    char device_type_str[10] = {0};
    char apn_str[APN_BUF_LEN * 4] = {0}; // URL 编码后会变长
    char request_interval_time_str[5] = {0};
    char sim_index_str[4] = {0};

    // - 获取本次上报时设备已使用流量，单位KB
    uint64_t amountFlow = g_qrzl_device_dynamic_data.flux_total_bytes;
    if (amountFlow > 0) {
        amountFlow /= 1024;
    } else {
        amountFlow = 0;
    }
    snprintf(amountFlow_str, sizeof(amountFlow_str), "%llu", amountFlow);

    // - 获取设备当前时间，格式 "yyyymmddhhmmss"
    if (get_local_time("%Y%m%d%H%M%S", time_str, sizeof(time_str)) == 0) {
        qrzl_log("当前时间: %s", time_str);
    } else {
        qrzl_log("获取时间失败");
    }

    // - SSID 需要进行 URL 编码防止中文或特殊字符在网络传输中乱码,或者终端软件对用户输入进行限制
    url_encode(g_qrzl_device_dynamic_data.wifi_ssid, wifi_ssid_encoded);

    // - WIFI密码 base64 加密
    qrzl_base64_encode_safe(g_qrzl_device_dynamic_data.wifi_key, strlen(g_qrzl_device_dynamic_data.wifi_key), wifi_key_encoded, sizeof(wifi_key_encoded));

    // - 获取当前电量百分比
    snprintf(remain_pwr_str, sizeof(remain_pwr_str), "%d", get_remain_power());

    // - 获取连接数
    snprintf(conn_cnt_str, sizeof(conn_cnt_str), "%d", g_qrzl_device_dynamic_data.conn_num);
    
    // - WIFI状态 1: 开启 0: 关闭
    snprintf(wifi_status_str, sizeof(wifi_status_str), "%d", g_qrzl_device_dynamic_data.wifi_enable);

    // - WIFI 广播状态  1: 隐藏 0: 不隐藏
    snprintf(wifi_hide_str, sizeof(wifi_hide_str), "%d", g_qrzl_device_dynamic_data.wifi_hide);

    // - 获取当前上行速率 upspeed
    snprintf(up_speed_bps_str, sizeof(up_speed_bps_str), "%llu", g_qrzl_device_dynamic_data.up_speed_bps);

    // - 获取当前下行速率 upspeed
    snprintf(down_speed_bps_str, sizeof(down_speed_bps_str), "%llu", g_qrzl_device_dynamic_data.down_speed_bps);

    // - 获取设备类型
    if (strlen(g_qrzl_device_static_data.hw_version) > 0) {
        if (strstr(g_qrzl_device_static_data.hw_version, "MZ") != NULL || strstr(g_qrzl_device_static_data.hw_version, "GS28V") != NULL) {
            snprintf(device_type_str, sizeof(device_type_str), "MIFI");
        } else if (strstr(g_qrzl_device_static_data.hw_version, "DZ") != NULL) {
            snprintf(device_type_str, sizeof(device_type_str), "DZ");
        } else if (strstr(g_qrzl_device_static_data.hw_version, "UZ") != NULL) {
            snprintf(device_type_str, sizeof(device_type_str), "UFI");
        } else {
            snprintf(device_type_str, sizeof(device_type_str), "Unknown");
        }
    }

    // - 获取apn信息
    build_apn_string(apn_str, sizeof(apn_str));
    qrzl_log("APN: %s", apn_str);

    // - 开机首次上报
    if (first_report_flag == 0) {
        first_report_flag = 1;
    }

    // - 上报间隔时间
    snprintf(request_interval_time_str, sizeof(request_interval_time_str), "%d", request_interval_time);

    // - 当前使用卡槽号
    int sim_index = get_device_current_sim_index();
    if (sim_index != -1) {
       switch (sim_index)
       {
        case 1:
            snprintf(sim_index_str, sizeof(sim_index_str), "1");
            break;
        case 2:
            snprintf(sim_index_str, sizeof(sim_index_str), "2");
            break;
        case 0:
            snprintf(sim_index_str, sizeof(sim_index_str), "3");
            break;  
        
        default:
            break;
       }
    }

    // TODO - SIM卡列表
    cJSON *sim_list = cJSON_CreateArray();
    
    cJSON *sim1 = cJSON_CreateObject();
    cJSON_AddStringToObject(sim1, "seq", "1");
    cJSON_AddStringToObject(sim1, "sim_iccid", g_qrzl_device_static_data.nvro_esim1_iccid);
    cJSON_AddStringToObject(sim1, "sim_imsi", g_qrzl_device_static_data.nvro_esim1_imsi);
    cJSON_AddItemToArray(sim_list, sim1);

    cJSON *sim2 = cJSON_CreateObject();
    cJSON_AddStringToObject(sim2, "seq", "2");
    cJSON_AddStringToObject(sim2, "sim_iccid", g_qrzl_device_static_data.nvro_esim2_iccid);
    cJSON_AddStringToObject(sim2, "sim_imsi", g_qrzl_device_static_data.nvro_esim2_imsi);
    cJSON_AddItemToArray(sim_list, sim2);

#ifdef QRZL_HAVE_3_ESIM_CARD
    cJSON *sim3 = cJSON_CreateObject();
    cJSON_AddStringToObject(sim3, "seq", "3");
    cJSON_AddStringToObject(sim3, "sim_iccid", g_qrzl_device_static_data.nvro_esim3_iccid);
    cJSON_AddStringToObject(sim3, "sim_imsi", g_qrzl_device_static_data.nvro_esim3_imsi);
    cJSON_AddItemToArray(sim_list, sim3);
#endif

    // 构建请求体
    cJSON *j_request_body = cJSON_CreateObject();
    cJSON_AddStringToObject(j_request_body, "imei", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(j_request_body, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(j_request_body, "mac", g_qrzl_device_static_data.mac);
    cJSON_AddStringToObject(j_request_body, "amount_end", amountFlow_str);
    cJSON_AddStringToObject(j_request_body, "current_time", time_str);
    cJSON_AddStringToObject(j_request_body, "ssid", wifi_ssid_encoded);
    cJSON_AddStringToObject(j_request_body, "key", wifi_key_encoded);
    cJSON_AddStringToObject(j_request_body, "remain_pwr", remain_pwr_str);
    cJSON_AddStringToObject(j_request_body, "conn_cnt", conn_cnt_str);
    cJSON_AddStringToObject(j_request_body, "mcc", g_qrzl_device_dynamic_data.mcc);
    cJSON_AddStringToObject(j_request_body, "mnc", g_qrzl_device_dynamic_data.mnc);
    cJSON_AddStringToObject(j_request_body, "lac", g_qrzl_device_dynamic_data.tac);
    cJSON_AddStringToObject(j_request_body, "cid", g_qrzl_device_dynamic_data.cid);
    cJSON_AddStringToObject(j_request_body, "soft_version", g_qrzl_device_static_data.soft_version);
    cJSON_AddStringToObject(j_request_body, "wifistatus", wifi_status_str);
    cJSON_AddStringToObject(j_request_body, "hidden", wifi_hide_str);
    cJSON_AddStringToObject(j_request_body, "sn", g_qrzl_device_static_data.sn);
    cJSON_AddStringToObject(j_request_body, "upspeed", up_speed_bps_str);
    cJSON_AddStringToObject(j_request_body, "downspeed", down_speed_bps_str);
    cJSON_AddStringToObject(j_request_body, "rssi", g_qrzl_device_dynamic_data.rssi);
    cJSON_AddStringToObject(j_request_body, "webPassword", g_qrzl_device_dynamic_data.web_password);
    cJSON_AddStringToObject(j_request_body, "rsrq", g_qrzl_device_dynamic_data.rsrq);
    cJSON_AddStringToObject(j_request_body, "sinr", "");
    cJSON_AddStringToObject(j_request_body, "pci", "");
    cJSON_AddStringToObject(j_request_body, "imsi", g_qrzl_device_dynamic_data.imsi);
    cJSON_AddStringToObject(j_request_body, "devicetype", device_type_str);
    cJSON_AddStringToObject(j_request_body, "apn", apn_str);
    cJSON_AddStringToObject(j_request_body, "apEncrypttype", g_qrzl_device_dynamic_data.wifi_encryp_type);
    cJSON_AddStringToObject(j_request_body, "wifi_filter_type", "");
    cJSON_AddStringToObject(j_request_body, "blacklist", "");
    cJSON_AddStringToObject(j_request_body, "whitelist", "");
    cJSON_AddStringToObject(j_request_body, "currentIp", g_qrzl_device_dynamic_data.current_wan_ip);
    cJSON_AddStringToObject(j_request_body, "powerOn", first_report_flag ? "1" : "0");
    cJSON_AddStringToObject(j_request_body, "intervalFlow", request_interval_time_str);
    cJSON_AddStringToObject(j_request_body, "mainSim", sim_index_str);
    // cJSON_AddStringToObject(j_request_body, "simList", ""); //数组
    cJSON_AddItemToObject(j_request_body, "simList", sim_list);

    // cJSON转成紧凑型字符串
    char *req = cJSON_PrintUnformatted(j_request_body);
    if (req) {
        snprintf(http_post_body, sizeof(http_post_body), "%s", req);
        free(req);
    }
    cJSON_Delete(j_request_body);
    
    // 构建POST请求
    int request_res = http_post(http_report_request_url, http_post_body, http_response, sizeof(http_response));

    if (request_res == HTTP_OK) {
        qrzl_log("设备信息上报成功");
        // 解析响应
        cJSON *j_response = cJSON_Parse(http_response);
        if (j_response) {
            response_handler(j_response);
            cJSON_Delete(j_response);
        } else {
            qrzl_log("响应解析失败");
        }
    } else {
        qrzl_log("设备信息上报失败");
    }

}

/**
 * 响应处理
 */
static void response_handler(cJSON* root)
{

    struct wifi_config_t wifi_config;
    memset(&wifi_config, 0, sizeof(wifi_config));
    init_wifi_config_value(&wifi_config);

    int is_change_wifi_info = 0; // 修改WIFI信息标识

    cJSON *j_limitSpeed = cJSON_GetObjectItem(root, "limitSpeed");
    if (j_limitSpeed && j_limitSpeed->type == cJSON_String) {
        int limit_speed = atoi(j_limitSpeed->valuestring);
        if (limit_speed >= 0) {
            qrzl_log("Start Limit net speed: %d Kbps",limit_speed);
            limit_net_speed(limit_speed,limit_speed);
        } else {
            qrzl_log("value error! limitSpeed must >= 0");
        }
    }

    cJSON *j_nextRptTime = cJSON_GetObjectItem(root, "nextRptTime");
    if (j_nextRptTime && j_nextRptTime->type == cJSON_String) {
        int nextRptTime = atoi(j_nextRptTime->valuestring);
        if (nextRptTime >= 30) {
            qrzl_log("Change nextRptTime: %ds",nextRptTime);
            request_interval_time = nextRptTime;
            cfg_set("qrzl_cloud_request_interval_time", j_nextRptTime->valuestring);
        } else {
            qrzl_log("value error! nextRptTime must >= 30");
        }
    }

    cJSON *j_ssidName = cJSON_GetObjectItem(root, "ssidName");
    if (j_ssidName && j_ssidName->type == cJSON_String) {
        char ssid_decoded[128] = {0};
        url_decode(j_ssidName->valuestring, ssid_decoded, sizeof(ssid_decoded));
        if (strlen(ssid_decoded) > 0) {
            snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", ssid_decoded);
            is_change_wifi_info = 1;
        } else {
            qrzl_log("ssidName decode error!");
        }
    }

    cJSON *j_ssidPass = cJSON_GetObjectItem(root, "ssidPass");
    if (j_ssidPass && j_ssidPass->type == cJSON_String) {
        if (strlen(j_ssidPass->valuestring) > 0) {
            snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_ssidPass->valuestring);
            is_change_wifi_info = 1;
        } else {
            qrzl_log("ssidPass len error!");
        }
    }

    cJSON *j_wifistatus = cJSON_GetObjectItem(root, "wifistatus");
    if (j_wifistatus && j_wifistatus->type == cJSON_String) {
        int wifi_status = atoi(j_wifistatus->valuestring);
        if (wifi_status == 0 || wifi_status == 1) {
            if (wifi_status == 1) {
                // 打开WIFI，关闭网络转发
                wifi_config.enable = 1;
                set_network_br0_disconnect(0);
            } else if (wifi_status == 0) {
                // 关闭WIFI，打开网络转发
                wifi_config.enable = 0;
                set_network_br0_disconnect(1);
            }
            is_change_wifi_info = 1;
        } else {
            qrzl_log("value error! wifistatus must be 0 or 1");
        }
    }

    cJSON *j_hidden = cJSON_GetObjectItem(root, "hidden");
    if (j_hidden && j_hidden->type == cJSON_String) {
        int wifi_hide = atoi(j_hidden->valuestring);
        if (wifi_hide == 0 || wifi_hide == 1) {
            wifi_config.hide = wifi_hide;
            is_change_wifi_info = 1;
        } else {
            qrzl_log("value error! hidden must be 0 or 1");
        }
    }

    cJSON *j_force_restart = cJSON_GetObjectItem(root, "force_restart");
    if (j_force_restart && j_force_restart->type == cJSON_String) {
        int force_restart = atoi(j_force_restart->valuestring);
        if (force_restart == 1) {
            qrzl_log("Factory restart device!");
            restart_device();
        }
    }

    cJSON *j_force_reset = cJSON_GetObjectItem(root, "force_reset");
    if (j_force_reset && j_force_reset->type == cJSON_String) {
        int force_reset = atoi(j_force_reset->valuestring);
        if (force_reset == 1) {
            qrzl_log("Factory reset device!");
            reset_device();
        }
    }

    // 切卡处理
    cJSON *j_switchSim = cJSON_GetObjectItem(root, "switchSim");
    if (j_switchSim && j_switchSim->type == cJSON_String) {
        int switch_sim = atoi(j_switchSim->valuestring);
        switch (switch_sim)
        {
            case 1:
                switch_sim_card_not_restart(1);
                break;
            case 2:
                switch_sim_card_not_restart(2);
                break;
            case 3:
                switch_sim_card_not_restart(0);
                break;
            
            default:
                qrzl_log("value error! switchSim must be 1, 2 or 3");
                break;
        }
    }




    cJSON *j_wan_type = cJSON_GetObjectItem(root, "wan_type");
    if (j_wan_type && j_wan_type->type == cJSON_String) {
        // TODO 暂未有处理
        qrzl_log("Get wan_type: %s",j_wan_type->valuestring);
    }

    cJSON *j_deviceUpgrade = cJSON_GetObjectItem(root, "deviceUpgrade");
    if (j_deviceUpgrade && j_deviceUpgrade->type == cJSON_String) {
        // TODO 暂未有处理
        qrzl_log("Get deviceUpgrade: %s",j_deviceUpgrade->valuestring);
    }

    cJSON *j_wifi_filter_type = cJSON_GetObjectItem(root, "wifi_filter_type");
    if (j_wifi_filter_type && j_wifi_filter_type->type == cJSON_String) {
        // TODO 暂未有处理
        qrzl_log("Get wifi_filter_type: %s",j_wifi_filter_type->valuestring);
    }

    cJSON *j_clrStaticsTime = cJSON_GetObjectItem(root, "clrStaticsTime");
    if (j_clrStaticsTime && j_clrStaticsTime->type == cJSON_String) {
        // TODO 暂未有处理
        qrzl_log("Get clrStaticsTime: %s",j_clrStaticsTime->valuestring);
    }

    cJSON *j_srvCurrTime = cJSON_GetObjectItem(root, "srvCurrTime");
    if (j_srvCurrTime && j_srvCurrTime->type == cJSON_String) {
        // TODO 暂未有处理
        qrzl_log("Get srvCurrTime: %s",j_srvCurrTime->valuestring);
    }

    cJSON *j_trafficRpt = cJSON_GetObjectItem(root, "trafficRptThreshold");
    if (j_trafficRpt && j_trafficRpt->type == cJSON_String) {
        // TODO 暂未有处理
        qrzl_log("Get trafficRptThreshold: %s",j_trafficRpt->valuestring);
    }

    // TODO blacklist、 whitelist、deviceCountSet、 apEncrypttype、upgradeFlag 暂不处理

    // 更新WIFI配置并生效
    if (is_change_wifi_info) {
        update_wifi_by_config(&wifi_config);
    }
    
}

// 云聊业务控制线程
void* start_yunliao_http_control_client(){

    // 防止开机时一开始没网
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }

    update_device_static_data();

    init_config();

    while (1)
    {
        device_info_report();
        sleep(request_interval_time);
    }
    
}