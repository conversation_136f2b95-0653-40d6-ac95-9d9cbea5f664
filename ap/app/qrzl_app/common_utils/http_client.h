#ifndef HTTP_CLIENT_H
#define HTTP_CLIENT_H

#include <stddef.h>

// 默认缓冲区大小
#define HTTP_RESPONSE_MAX  8192
#define HTTP_REQUEST_MAX   4096

// 返回值定义
#define HTTP_OK            0
#define HTTP_ERROR        -1

// HTTP GET
int http_get(const char *url, char *response, size_t resp_size);
int https_get(const char *url, char *response, size_t resp_size);

// HTTP POST (content-type: application/json)
int http_post(const char *url, const char *body, char *response, size_t resp_size);
int https_post(const char *url, const char *body, char *response, size_t resp_size);

#endif // HTTP_CLIENT_H
