#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../common_utils/http_client.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"
#include "../common_utils/md5.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define HASH_SIZE 16



// 计算 MD5 并转为大写十六进制字符串
// input  : 输入的字符串
// output : 存放 MD5 结果的缓冲区，长度至少 33 字节（32 位 MD5 + '\0'）
void md5_to_upper(const char *input, char *output)
{   
    int i;
    unsigned char digest[HASH_SIZE];  // MD5结果是16字节
    lpa_MD5_CTX ctx;

    // 初始化并计算MD5
    lpa_MD5_Init(&ctx);
    lpa_MD5_Update(&ctx, input, strlen(input));
    lpa_MD5_Final(digest, &ctx);

    // 转为大写16进制字符串
    for (i = 0; i < HASH_SIZE; i++) {
        sprintf(output + (i * 2), "%02X", digest[i]);  // 注意 %02X 大写
    }
    output[32] = '\0';  // 结尾符
}

/**
 * IOT物联网平台获取token接口
 * @param appid_str 输出appid字符串
 * @param appid_len appid字符串长度
 * @param token_str 输出token字符串
 * @param token_len token字符串长度
 * @param url 请求的URL地址
 * @param method 请求的方法名
 * @param userCode 用户编码
 * @param supplierCode 供应商编码
 * @param requestNo 请求编号
 * @param key API密钥
 * @return 0 成功， -1 失败
 */
int iot_get_token(char *appid_str, size_t appid_len, char *token_str, size_t token_len, const char *url, const char *method, const char *userCode, const char *supplierCode, const char *requestNo, const char *key)
{
    char timestamp[32] = {0};
    snprintf(timestamp, sizeof(timestamp), "%ld000", time(NULL));  // 13位时间戳

    // 拼接签名参数(这里的参数要按ASCLL升序，空参数不参与签名，sign本身不参与签名)
    char stringA[512] = {0};
    snprintf(stringA, sizeof(stringA),
             "method=%s&requestNo=%s&supplierCode=%s&timestamp=%s&userCode=%s",
             method, requestNo, supplierCode, timestamp, userCode);
    
    // 拼接 key
    char stringSignTemp[600] = {0};
    snprintf(stringSignTemp, sizeof(stringSignTemp), "%s&key=%s", stringA, key);

    // 生成 sign
    char sign[64] = {0};
    md5_to_upper(stringSignTemp, sign);
    qrzl_log("global_sign_hash: %s \n", sign);
    // 构建 JSON 请求体
    cJSON *root = cJSON_CreateObject();
    if (!root) {
        qrzl_log("cJSON_CreateObject failed.");
        return -1;
    }
    cJSON_AddStringToObject(root, "method", method);
    cJSON_AddStringToObject(root, "requestNo", requestNo);
    cJSON_AddStringToObject(root, "supplierCode", supplierCode);
    cJSON_AddStringToObject(root, "timestamp", timestamp);
    cJSON_AddStringToObject(root, "userCode", userCode);
    cJSON_AddStringToObject(root, "sign", sign);

    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char *json_str = cJSON_PrintUnformatted(root);
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        free(json_str);
    }
    cJSON_Delete(root);

    // 发送 POST 请求
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};
    if (https_post(url, request_body, http_response_content, QRZL_HTTP_RESPONSE_MAX) != 0) {
        qrzl_log("获取token请求异常,请重试!");
        return -1;
    }
   // 打印原始响应内容
    qrzl_log("服务器响应内容: %s", http_response_content);

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(http_response_content);
    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return -1;
    }

    // 先取 errorCode 判断通信是否成功
    cJSON *json_errorCode = cJSON_GetObjectItem(json_response, "errorCode");
    if (!json_errorCode || json_errorCode->type != cJSON_String) {
        qrzl_log("响应缺少 errorCode 字段.");
        cJSON_Delete(json_response);
        return -1;
    }
    if (strcmp(json_errorCode->valuestring, "SUCCESS") != 0) {
        cJSON *json_msg = cJSON_GetObjectItem(json_response, "returnMsg");
        if (json_msg && json_msg->type == cJSON_String) {
            qrzl_log("请求失败,errorCode=%s, returnMsg=%s",
                    json_errorCode->valuestring, json_msg->valuestring);
        } else {
            qrzl_log("请求失败,errorCode=%s", json_errorCode->valuestring);
        }
        cJSON_Delete(json_response);
        return -1;
    }

    // 取 data 对象
    cJSON *json_data = cJSON_GetObjectItem(json_response, "data");
    if (!json_data || json_data->type != cJSON_Object) {
        qrzl_log("响应缺少 data 字段.");
        cJSON_Delete(json_response);
        return -1;
    }

    // data.code
    cJSON *json_code = cJSON_GetObjectItem(json_data, "code");
    if (!json_code || json_code->type != cJSON_String) {
        qrzl_log("data 缺少 code 字段.");
        cJSON_Delete(json_response);
        return -1;
    }
    if (strcmp(json_code->valuestring, "0") != 0) {
        cJSON *json_msg = cJSON_GetObjectItem(json_data, "message");
        if (json_msg && json_msg->type == cJSON_String) {
            qrzl_log("请求失败, code=%s, message=%s",
                    json_code->valuestring, json_msg->valuestring);
        } else {
            qrzl_log("请求失败, code=%s", json_code->valuestring);
        }
        cJSON_Delete(json_response);
        return -1;
    }

    // data.token
    cJSON *json_token = cJSON_GetObjectItem(json_data, "token");
    if (!json_token || json_token->type != cJSON_String) {
        qrzl_log("响应缺少 token 字段.");
        cJSON_Delete(json_response);
        return -1;
    }
    snprintf(token_str, token_len, "%s", json_token->valuestring);

    // 固定 appid
    // snprintf(appid_str, appid_len, "%s", "C5010200A9992003892051");

    // data.timeOut
    cJSON *json_timeout = cJSON_GetObjectItem(json_data, "timeOut");
    if (json_timeout && json_timeout->type == cJSON_String) {
        qrzl_log("Token: %s, 有效期: %s 秒", token_str, json_timeout->valuestring);
    } else {
        qrzl_log("Token: %s", token_str);
    }

    cJSON_Delete(json_response);
    qrzl_log("获取 Iot token 成功.");
    return 0;
}
