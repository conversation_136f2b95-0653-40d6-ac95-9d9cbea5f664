#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../common_utils/http_client.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define KEY_ONE_LINK_GET_TOKEN_URL       "ONE_LINK_customers_get_token_url"
#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define CURL_REQUEST_ERROR -1
#define CURL_REQUEST_OK 0

static char one_link_customers_get_token_url[256] = {0};

static int init_customers_info()
{
    //由于展谷电信移动认证用的同一个通用接口，所以随便取一个URL就行
    int res_url = cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (res_url != 0) {
        qrzl_log("Configuration item %s get error!", KEY_ONE_LINK_GET_TOKEN_URL);
    }
}

int zhangu_get_token_and_appid(char *appid_str, size_t appid_len, char *token_str, size_t token_len)
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    //拼接上下线URL
    const char *interface_str = "/admin/transfer/api/CarrWiFi/GetQiRuiApiToken";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);

    char iccid_str[22] = {0};
    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送HTTP POST请求
    int ret = http_post(request_url, request_body, response_body, sizeof(response_body));
    if (ret != 0) {
        qrzl_log("HTTP发送POST请求失败, ret=%d", ret);
        return CURL_REQUEST_ERROR;
    }
    // 解析返回结果
    cJSON *resp_json = cJSON_Parse(response_body);
    if (resp_json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return CURL_REQUEST_ERROR;
    }
    if (!resp_json || resp_json->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    // 提取并检查 code
    cJSON *j_code = cJSON_GetObjectItem(resp_json, "code");
    if (!j_code || !cJSON_IsNumber(j_code)) {
        qrzl_log("返回缺少或非法的 code 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    if(j_code->valueint != 200) {
        qrzl_log("ZHANGU返回 code=%d, 获取 token 和 appid 失败", j_code->valueint);
        cJSON* j_msg = cJSON_GetObjectItem(resp_json, "msg");
        if(j_msg && cJSON_IsString(j_msg)) {
            qrzl_log("msg=%s", j_msg->valuestring);
        }
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    cJSON *j_data = cJSON_GetObjectItem(resp_json, "data");
    if (!j_data || !cJSON_IsObject(j_data)) {
        qrzl_log("返回缺少或非法的 data 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    cJSON *j_appid = cJSON_GetObjectItem(j_data, "appId");
    if(!j_appid || !cJSON_IsString(j_appid)) {
        qrzl_log("返回缺少或非法的 appid 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }else {
        snprintf(appid_str, appid_len, "%s", j_appid->valuestring);
        qrzl_log("获取appid成功:%s", appid_str);
    }
    cJSON *j_token = cJSON_GetObjectItem(j_data, "token");
    if(!j_token || !cJSON_IsString(j_token)) {
        qrzl_log("返回缺少或非法的 token 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }else {
        snprintf(token_str, token_len, "%s", j_token->valuestring);
        qrzl_log("获取token成功:%s", token_str);
    }

    cJSON_Delete(resp_json);
    return CURL_REQUEST_ERROR;    
}
int zhangu_get_appkey_and_secretkey(char *appkey_str, size_t appkey_len, char *secretkey_str, size_t secretkey_len)
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    //拼接上下线URL
    const char *interface_str = "/admin/transfer/api/CarrWiFi/GetQiRuiApiToken";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);

    char iccid_str[22] = {0};
    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送HTTP POST请求
    int ret = http_post(request_url, request_body, response_body, sizeof(response_body));
    if (ret != 0) {
        qrzl_log("HTTP发送POST请求失败, ret=%d", ret);
        return CURL_REQUEST_ERROR;
    }
    // 解析返回结果
    cJSON *resp_json = cJSON_Parse(response_body);
    if (resp_json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return CURL_REQUEST_ERROR;
    }
    if (!resp_json || resp_json->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    // 提取并检查 code
    cJSON *j_code = cJSON_GetObjectItem(resp_json, "code");
    if (!j_code || !cJSON_IsNumber(j_code)) {
        qrzl_log("返回缺少或非法的 code 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    if(j_code->valueint != 200) {
        qrzl_log("ZHANGU返回 code=%d, 获取 appkey 和 secretkey 失败", j_code->valueint);
        cJSON* j_msg = cJSON_GetObjectItem(resp_json, "msg");
        if(j_msg && cJSON_IsString(j_msg)) {
            qrzl_log("msg=%s", j_msg->valuestring);
        }
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    cJSON *j_data = cJSON_GetObjectItem(resp_json, "data");
    if (!j_data || !cJSON_IsObject(j_data)) {
        qrzl_log("返回缺少或非法的 data 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    cJSON *j_appkey = cJSON_GetObjectItem(j_data, "appKey");
    if(!j_appkey || !cJSON_IsString(j_appkey)) {
        qrzl_log("返回缺少或非法的 appKey 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }else {
        snprintf(appkey_str, appkey_len, "%s", j_appkey->valuestring);
        qrzl_log("获取appkey成功:%s", appkey_str);
    }
    cJSON *j_sign = cJSON_GetObjectItem(j_data, "sign");
    if(!j_sign || !cJSON_IsString(j_sign)) {
        qrzl_log("返回缺少或非法的 sign 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }else {
        snprintf(secretkey_str, secretkey_len, "%s", j_sign->valuestring);
        qrzl_log("获取secretkey成功:%s", secretkey_str);
    }

    cJSON_Delete(resp_json);
    return CURL_REQUEST_ERROR;
}
