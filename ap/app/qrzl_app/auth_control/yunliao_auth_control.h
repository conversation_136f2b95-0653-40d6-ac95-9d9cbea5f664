#ifndef __QRZL_YUNLIAO_AUTH_CONTROL_H_
#define __QRZL_YUNLIAO_AUTH_CONTROL_H_

/**
 * IOT物联网平台获取token接口
 * @param appid_str 输出appid字符串
 * @param appid_len appid字符串长度
 * @param token_str 输出token字符串
 * @param token_len token字符串长度
 * @param url 请求的URL地址
 * @param method 请求的方法名
 * @param userCode 用户编码
 * @param supplierCode 供应商编码
 * @param requestNo 请求编号
 * @param key API密钥
 * @return 0 成功， -1 失败
 */
int iot_get_token(char *appid_str, size_t appid_len, char *token_str, size_t token_len, const char *url, const char *method, const char *userCode, const char *supplierCode, const char *requestNo, const char *key);

#endif