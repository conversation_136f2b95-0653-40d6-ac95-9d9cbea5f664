#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../common_utils/http_client.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"



extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define KEY_ONE_LINK_GET_TOKEN_URL       "ONE_LINK_customers_get_token_url"
#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define CURL_REQUEST_ERROR -1
#define CURL_REQUEST_OK 0

static char one_link_customers_get_token_url[256] = {0};

static int init_customers_info()
{
    //由于酷鱼电信移动认证用的同一个通用接口，所以随便取一个URL就行
    int res_url = cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (res_url != 0) {
        qrzl_log("Configuration item %s get error!", KEY_ONE_LINK_GET_TOKEN_URL);
    }
}
// 获取当前本地时间字符串，格式："YYYY-MM-DD HH:MM:SS"
static void get_local_datetime(char *buffer, size_t size) {
    if (!buffer || size < 20) return;  // 安全检查：最小需 20 字节

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

/**
 * 酷鱼 上报设备上下线
 * @param push_type 上报类型 0 下线， 1 下线
 * @param terminalMac 终端MAC
 * @return 0 成功  -1 失败
 */
int ky_report_device_line_state(int push_type, char *terminalMac)
{
     init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), ':');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), ':');

    //拼接上下线URL
    const char *interface_str = (push_type == 0) ? "/terminalOffline" : "/terminalOnline";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);

    // 构建 JSON 请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }
    cJSON_Delete(root);

    // 发送请求
    int ret = http_post(request_url, request_body, response_body,sizeof(response_body));
    if (ret != 0) {
        qrzl_log("HTTP 发送 POST 请求失败, ret=%d", ret);
        return CURL_REQUEST_ERROR;
    }

    // 解析响应
    cJSON *resp_json = cJSON_Parse(response_body);
    if (!resp_json || !cJSON_IsObject(resp_json)) {
        qrzl_log("响应格式错误: %s", response_body);
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    int result = CURL_REQUEST_ERROR;
    cJSON *j_status = cJSON_GetObjectItem(resp_json, "status");

    if (j_status && cJSON_IsNumber(j_status) && j_status->valueint == 0) {
        qrzl_log("KY -> 终端[%s] %s上报成功",
                 terminalMac_str,
                 (push_type == 0 ? "下线" : "上线"));
        result = CURL_REQUEST_OK;
    } else {
        qrzl_log("KY -> 终端[%s] %s上报失败",
                 terminalMac_str,
                 (push_type == 0 ? "下线" : "上线"));
        cJSON *j_message = cJSON_GetObjectItem(resp_json, "message");
        if (j_message && cJSON_IsString(j_message)) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
    }

    cJSON_Delete(resp_json);
    return result;
}


/**
 * 酷鱼 发送短信
 * @param terminalMac 终端mac
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int ky_send_sms(char *terminalMac, char *phoneNum)
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), ':');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), ':');

    // 拼接 URL
    snprintf(request_url, sizeof(request_url), "%s/sendSms",
             one_link_customers_get_token_url);

    // 构建 JSON 请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    cJSON_AddStringToObject(root, "phoneNum", phoneNum);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }
    cJSON_Delete(root);

    // 发送请求
    int ret = http_post(request_url, request_body, response_body,sizeof(response_body));
    if (ret != 0) {
        qrzl_log("HTTP 发送 POST 请求失败, ret=%d", ret);
        return CURL_REQUEST_ERROR;
    }

    // 解析响应
    cJSON *resp_json = cJSON_Parse(response_body);
    if (!resp_json || !cJSON_IsObject(resp_json)) {
        qrzl_log("响应格式错误: %s", response_body);
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    cJSON *j_status = cJSON_GetObjectItem(resp_json, "status");
    int result = CURL_REQUEST_ERROR;

    if (j_status && cJSON_IsNumber(j_status) && j_status->valueint == 0) {
        qrzl_log("KY -> 发送短信成功!");
        result = CURL_REQUEST_OK;
    } else {
        qrzl_log("KY -> 发送短信失败!");
        cJSON *j_message = cJSON_GetObjectItem(resp_json, "message");
        if (j_message && cJSON_IsString(j_message)) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
    }

    cJSON_Delete(resp_json);
    return result;
}



/**
 * 酷鱼 终端设备认证
 * @param terminalMac 终端mac
 * @param verifyCode 验证码
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int ky_terminal_auth(char *terminalMac, char *verifyCode, char *phoneNum)
{
    init_customers_info();

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), ':');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), ':');

    // 拼接 URL
    snprintf(request_url, sizeof(request_url), "%s/terminalAuth",
             one_link_customers_get_token_url);

    // 构建 JSON 请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    cJSON_AddStringToObject(root, "phoneNum", phoneNum);
    cJSON_AddStringToObject(root, "verifyCode", verifyCode);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }
    cJSON_Delete(root);

    // 发送请求
    int ret = http_post(request_url, request_body, response_body,sizeof(response_body));
    if (ret != 0) {
        qrzl_log("HTTP 发送 POST 请求失败, ret=%d", ret);
        return CURL_REQUEST_ERROR;
    }

    // 解析响应
    cJSON *resp_json = cJSON_Parse(response_body);
    if (!resp_json || !cJSON_IsObject(resp_json)) {
        qrzl_log("响应格式错误: %s", response_body);
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    cJSON *j_status = cJSON_GetObjectItem(resp_json, "status");
    int result = CURL_REQUEST_ERROR;

    if (j_status && cJSON_IsNumber(j_status) && j_status->valueint == 0) {
        qrzl_log("KY -> 设备认证成功!");
        result = CURL_REQUEST_OK;
    } else {
        qrzl_log("KY -> 设备认证失败!");
        cJSON *j_message = cJSON_GetObjectItem(resp_json, "message");
        if (j_message && cJSON_IsString(j_message)) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
    }
    //暂时没看出来这里的data有什么用
    cJSON *j_data = cJSON_GetObjectItem(resp_json, "data");
    {

    }
    cJSON_Delete(resp_json);
    return result;
}


/**
 * 酷鱼 获取设备上网认证终端MAC
 */
char* ky_get_authed_list_str()
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 拼接 URL
    snprintf(request_url, sizeof(request_url), "%s/terminalList",
             one_link_customers_get_token_url);

    char iccid_str[22] = {0};
    char mac_str[33] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), ':');

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发起POST请求
    int request_res = http_post(request_url, request_body, response_body,sizeof(response_body));

    if (request_res != HTTP_OK) {
        return NULL;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return NULL;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return NULL;
    }
    //先判断二次认证开关开关
    cJSON *j_authCode = cJSON_GetObjectItem(json_response, "authCode");
    if (!j_authCode || !cJSON_IsNumber(j_authCode)) {
        qrzl_log("authCode is NULL or not a number.");
        cJSON_Delete(json_response);
        return NULL;
    }else {
        char current_switch[32] = {0};
        // 获取当前的认证开关状态
        get_authentic_switch(current_switch, sizeof(current_switch));
        qrzl_log("[认证开关] 请求: %d, 当前: %s", j_authCode->valueint, current_switch);
        // 验证输入值的合法性
        if (j_authCode->valueint == 0) {
            if (strcmp(current_switch, "1") != 0) {
                set_authentic_switch("1");
                qrzl_log("[认证开关] 已开启二次认证功能");
            } else {
                qrzl_log("[认证开关] 二次认证已处于开启状态");
            }
        } 
        else if (j_authCode->valueint == 1) {
            if (strcmp(current_switch, "0") != 0) {
                set_authentic_switch("0");
                qrzl_log("[认证开关] 已关闭二次认证功能");
            } else {
                qrzl_log("[认证开关] 二次认证已处于关闭状态");
            }
        }
        else {
            qrzl_log("[认证开关] 错误的值: %d,只接受 '0'（关闭）或 '1'（开启）", j_authCode->valueint);
        }
    }
    cJSON *j_status = cJSON_GetObjectItem(json_response, "status");
    if (!j_status || j_status->type != cJSON_Number) {
        qrzl_log("status is NULL or not a number.");
        cJSON_Delete(json_response);
        return NULL;
    }   

    if (j_status->valueint != 0) {
        qrzl_log("获取已认证终端设备MAC失败!");
        cJSON *j_message = cJSON_GetObjectItem(json_response, "message");
        if (j_message && j_message->type == cJSON_String) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
        cJSON_Delete(json_response);
        return NULL;
    }
    
    // data 数组
    cJSON *j_data = cJSON_GetObjectItem(json_response, "data");
    if (!j_data || !cJSON_IsArray(j_data)) {
        qrzl_log("data is NULL or not an array.");
        cJSON_Delete(json_response);
        return NULL;
    }

    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 

    // 动态字符串构造
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(json_response);
        return NULL;
    }
    authed_mac_list[0] = '\0';
    int i;
    for (i = 0; i < cJSON_GetArraySize(j_data); ++i) {
        cJSON *item = cJSON_GetArrayItem(j_data, i);
        if (!item) continue;

        cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
        cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");

        if (!item_mac || !cJSON_IsString(item_mac) ||
            !expireTime || !cJSON_IsString(expireTime)) {
            continue;
        }

        // 时间比较
        if (strcmp(expireTime->valuestring, datetime) < 0) {
            continue; // 已过期
        }

        // 转换 MAC 格式
        char formatted_mac[32] = {0};
        convert_mac_format(item_mac->valuestring, formatted_mac,
                        sizeof(formatted_mac), ':');

        // 拼接
        size_t used = strlen(authed_mac_list);
        size_t needed = strlen(formatted_mac) + 2;
        if (used + needed >= buffer_size) break;
        strcat(authed_mac_list, formatted_mac);
        strcat(authed_mac_list, ";");
    }

    cJSON_Delete(json_response);
    return authed_mac_list;  // 需要调用者 free()
}