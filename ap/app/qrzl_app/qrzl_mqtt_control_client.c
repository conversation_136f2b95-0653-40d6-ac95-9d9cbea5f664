#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include <sys/time.h>
#include <unistd.h>
#include <unistd.h>
#include <curl/curl.h>

#include "qrzl_mqtt_control_client.h"
#include "qrzl_utils.h"
#include "MQTTClient.h"
#include "common_utils/cjson.h"

#include "cloud_control/xunyou_mqtt_control.h"


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static char mqtt_server[128] = {0};
static char mqtt_username[128] = {0};
static char mqtt_password[128] = {0};

/* ========================================= start mqtt type 1类型的处理 ==================================================================== */
#ifdef QRZL_MQTT_CLIENT_TYPE_1
static pthread_mutex_t t1_msg_handler_lock;  // 定义T1类型mqtt消息处理互斥锁，防止多线程同时操作

static uint32_t t1_publish_interval = 300;


int t1_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 15;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    char receive_topic[256] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_HMM
    snprintf(receive_topic, sizeof(receive_topic), "iot/%s/%s/%s", "ZXIC", "803P42U1701", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_YT
    snprintf(receive_topic, sizeof(receive_topic), "appprod_signTopic/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_HX
    snprintf(receive_topic, sizeof(receive_topic), "iotv3/%s/%s/%s", "lainiiot", "v1", g_qrzl_device_static_data.nvro_esim1_iccid);
#elif QRZL_APP_CUSTOMIZATION_SHAYIN
    snprintf(receive_topic, sizeof(receive_topic), "iotv3/%s/%s/%s", "ZXIC", "803P42U1701", g_qrzl_device_static_data.imei);
#endif
    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}

void t1_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    qrzl_log("Message with token %d delivered", dt);
}

int t1_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_HMM
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "ZXIC", "803P42U1701");
#elif QRZL_APP_CUSTOMIZATION_YT
    snprintf(topic, sizeof(topic), "appprod_statusTopic/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_HX
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "lainiiot", "v1");
//沙音用户用的是HMM的通道，不过不可直接用HMM的宏来定义
#elif QRZL_APP_CUSTOMIZATION_SHAYIN
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "ZXIC", "803P42U1701");    
#endif
    char payload[2048] = {0};
    snprintf(payload, sizeof(payload), "{");
    snprintf(payload, sizeof(payload),
            "%s\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);
#ifdef QRZL_APP_CUSTOMIZATION_HX
    snprintf(payload,sizeof(payload),"%s,\"cardno\":\"%s\"",payload,g_qrzl_device_static_data.nvro_esim1_iccid);
#endif
    snprintf(payload, sizeof(payload),
            "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);

    snprintf(payload, sizeof(payload),
            "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    
    snprintf(payload, sizeof(payload),
            "%s,\"simNum\":%d", payload, 2);
    snprintf(payload, sizeof(payload),
            "%s,\"remainPwr\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);
    
    char currentTime[64] = {0};
    get_local_time("%Y-%m-%d %H:%M:%S", currentTime, sizeof(currentTime));
    snprintf(payload, sizeof(payload),
            "%s,\"currentTime\":\"%s\"", payload, currentTime);
    
    snprintf(payload, sizeof(payload),
            "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload),
            "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload),
            "%s,\"hidden\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide);
    snprintf(payload, sizeof(payload),
            "%s,\"connCnt\":%d", payload, g_qrzl_device_dynamic_data.conn_num);
    snprintf(payload, sizeof(payload),
            "%s,\"rssi\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
    snprintf(payload, sizeof(payload),
            "%s,\"dayFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_day_total_bytes/1024); // 这个服务商的流量单位是KB
    snprintf(payload, sizeof(payload),
            "%s,\"monthFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_month_total_bytes/1024);
    snprintf(payload, sizeof(payload),
            "%s,\"version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"speedLimit\":%lld", payload, get_down_limit_net_speed());
    snprintf(payload, sizeof(payload),
            "%s,\"heartbeat\":%d", payload, t1_publish_interval);
    snprintf(payload, sizeof(payload),
            "%s,\"disconn\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn);
    
    // 以下是simList 列表
    snprintf(payload, sizeof(payload), "%s,\"simList\":[", payload);
    snprintf(payload, sizeof(payload), "%s{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    if (strncmp(g_qrzl_device_static_data.nvro_esim1_iccid, g_qrzl_device_dynamic_data.iccid, sizeof(g_qrzl_device_static_data.nvro_esim1_iccid)) == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    }

    snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    if (strncmp(g_qrzl_device_static_data.nvro_esim2_iccid, g_qrzl_device_dynamic_data.iccid, sizeof(g_qrzl_device_static_data.nvro_esim2_iccid)) == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    }
    // 如果有外卡就上传外卡
    if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0)
    {
        snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    }
    snprintf(payload, sizeof(payload), "%s]", payload);
    // 以上是simList 列表

    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    printf("\n printf -> mqtt payload: %s\n", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        qrzl_log("Message published!");
    }
    return 0;
}

int t1_order_msg_handler(cJSON* j_value, MQTTClient* client_p)
{
    int ret;

    cJSON *j_reset = cJSON_GetObjectItem(j_value, "reSet");
    if (j_reset != NULL && cJSON_IsNumber(j_reset))
    {
        if (j_reset->valueint == 1)
        {
            ret = reset_device();
            return ret;
        }
    }

    cJSON *j_reboot = cJSON_GetObjectItem(j_value, "reboot");
    if (j_reboot != NULL && cJSON_IsNumber(j_reboot))
    {
        if (j_reboot->valueint == 1)
        {
            ret = restart_device();
            return ret;
        }
    }

    cJSON *j_ssid = cJSON_GetObjectItem(j_value, "ssid");
    cJSON *j_password = cJSON_GetObjectItem(j_value, "password");
    if (j_ssid != NULL && cJSON_IsString(j_ssid) && j_password != NULL && cJSON_IsString(j_password))
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid->valuestring);
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_password->valuestring);
        update_wifi_by_config(&wifi_config);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON *j_hidden = cJSON_GetObjectItem(j_value, "hidden");
    if (j_hidden != NULL && cJSON_IsNumber(j_hidden))
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if(j_hidden->valueint==0){
            wifi_config.hide = 1;
        }
        else{
            wifi_config.hide = 0;
        }
        update_wifi_by_config(&wifi_config);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON* j_get = cJSON_GetObjectItem(j_value, "get");
    if (j_get != NULL && cJSON_IsBool(j_get))
    {
        if (cJSON_IsTrue(j_get))
        {
            ret = t1_publish_device_info(client_p);
            return ret;
        }
    }

    cJSON* j_disconn = cJSON_GetObjectItem(j_value, "disconn");
    if (j_disconn != NULL && cJSON_IsNumber(j_disconn))
    {
        if (j_disconn->valueint == 0)
        {
            set_network_br0_disconnect(0);
        }
        else if (j_disconn->valueint == 1)
        {
            set_network_br0_disconnect(1);
        }
        t1_publish_device_info(client_p);
        return -1;
    }

    cJSON* j_speed_limit = cJSON_GetObjectItem(j_value, "speedLimit");
    if (j_speed_limit != NULL && cJSON_IsNumber(j_speed_limit))
    {
        limit_net_speed(j_speed_limit->valueint, j_speed_limit->valueint);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON* j_month_flow = cJSON_GetObjectItem(j_value, "monthFlow");
    if (j_month_flow != NULL && cJSON_IsNumber(j_month_flow) && j_month_flow->valueint >= 0)
    {
        char flux_month_total[21] = {0};
        snprintf(flux_month_total, sizeof(flux_month_total), "%lld", (long long)j_month_flow->valueint * 1024);
        cfg_set("flux_month_total", flux_month_total);
        t1_publish_device_info(client_p);
        return 0;
    }

    cJSON* j_switch = cJSON_GetObjectItem(j_value, "switch");
    if (j_switch != NULL && cJSON_IsString(j_switch))
    {
        int switch_crad_count = 0;
        while (g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
        {
            qrzl_log("正在测网，不能切卡");
            sleep(5);
            switch_crad_count++;
        }
        
        int ret;
        int tmp = atoi(j_switch->valuestring);
        if (tmp == 1)
        {
            ret = switch_sim_card_not_restart(1);
        }
        else if (tmp == 2)
        {
            ret = switch_sim_card_not_restart(2);
        }
        else if (tmp == 3)
        {
            ret = switch_sim_card_not_restart(0);
        }
        
        if (tmp > 0 && tmp < 4 && ret == 0)
        {
            // 暂时不需要做任何操作
        }
        
        t1_publish_device_info(client_p);
        return 0;
    }
    
    cJSON* j_heartbeat = cJSON_GetObjectItem(j_value, "heartbeat");
    if (j_heartbeat != NULL && cJSON_IsString(j_heartbeat))
    {
        int tmp = atoi(j_heartbeat->valuestring);
        if (tmp < 30)
        {
            qrzl_err("心跳间隔时间不能小于30秒");
        }
        else
        {
            t1_publish_interval = tmp;
        }
        
        t1_publish_device_info(client_p);
        return 0;
    }
    return 0;
}

int t1_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    cJSON *j_value = cJSON_Parse((char*)message->payload);
    if (j_value != NULL)
    {
        if (cJSON_IsObject(j_value))
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            t1_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        cJSON_Delete(j_value);
    }
    
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void t1_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    t1_mqtt_connect(client_p);
    t1_publish_device_info(client_p);
}

void* t1_publish_loop(void* client_ptr) {
    qrzl_log("t1_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;
    uint32_t now_sleep_time_total = 600;
    uint32_t sleep_time = 3;
    int current_conn_num = 10; // 默认给10，这是为了后面判断方便
    char sta_count[3] = {0};
    int ret;

    while (1) {
        now_sleep_time_total += sleep_time;
#ifdef JCV_HW_MZ804_V1_4
    ret = cfg_get_item("sta_count", sta_count, 3);
    if (ret == 0) {
        current_conn_num = atoi(sta_count);
    }
    if (current_conn_num > 0) {
        if (now_sleep_time_total >= t1_publish_interval) {
            t1_publish_device_info(client_p);
            now_sleep_time_total = 0;
        }
    } else {
        if (now_sleep_time_total >= 600) {
            t1_publish_device_info(client_p);
            now_sleep_time_total = 0;
        }
    }
#else
    if (now_sleep_time_total >= t1_publish_interval) {
        t1_publish_device_info(client_p);
        now_sleep_time_total = 0;
    }
#endif

        sleep(sleep_time);
    }
    return NULL;
}

static void t1_cloud_client_start()
{
    update_device_static_data();
    
    if (pthread_mutex_init(&t1_msg_handler_lock, NULL) != 0) {
        qrzl_log("t1_msg_handler_lock init failed\n");
    }

    MQTTClient client;

    MQTTClient_create(&client, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, t1_mqtt_connlost, t1_message_arrived, t1_on_message_delivered);

    t1_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, t1_publish_loop, &client) != 0) {
        qrzl_err("Failed to create publish thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);
    pthread_mutex_destroy(&t1_msg_handler_lock);
    return;
}

/* ========================================= end mqtt type 1类型的处理 ==================================================================== */
#endif /* QRZL_MQTT_CLIENT_TYPE_1 */

/* ========================================= start mqtt ky 类型的处理 ==================================================================== */
#ifdef QRZL_MQTT_CLIENT_KY

static uint64_t ky_last_publish_rx_byte_flow = 0;
static uint64_t ky_last_publish_tx_byte_flow = 0;
static int64_t ky_publish_interval_ms = 300 * 1000;
static pthread_t ky_shutdown_thread_id = 0;
static int ky_shutdown_time_s = 0; // 关机时间 秒

static void *ky_shutdown_thread(void *arg)
{
    qrzl_log("ky sleep %d s shutdown", ky_shutdown_time_s);
    sleep(ky_shutdown_time_s);
    qrzl_log("ky_shutdown_thread: 开始关机");
    shutdown_device();
    return NULL;
}

int ky_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    snprintf(topic, sizeof(topic), "mifiiot/%s/server", g_qrzl_device_static_data.sn);
    char payload[2048] = {0};
    int now_sim_index = get_device_current_sim_index_by_data();
    snprintf(payload, sizeof(payload), "{");

    snprintf(payload, sizeof(payload),
            "%s\"api_version\":\"1.0\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"config_version\":\"1.0\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"upload_data_type\":\"normal\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"cur_sim_id\":\"%d\"", payload, now_sim_index);
    // cur_sim_cato 不知道什么意思
    // snprintf(payload, sizeof(payload),
    //         "%s,\"cur_sim_cato\":\"%d\"", payload, -1);

    // sim_info start
    snprintf(payload, sizeof(payload),
            "%s,\"sim_info\":[", payload);

    int lte_rsrp = atoi(g_qrzl_device_dynamic_data.lte_rsrp);
    int sim_signal_level = 0;
    if (lte_rsrp >= -85) {
        sim_signal_level = 5;
    } else if (lte_rsrp >= -95)
    {
        sim_signal_level = 4;
    } else if (lte_rsrp >= -105)
    {
        sim_signal_level = 3;
    } else if (lte_rsrp >= -115)
    {
        sim_signal_level = 2;
    } else if (lte_rsrp >= -199)
    {
        sim_signal_level = 1;
    }
    
    

    // ================== esim1 start ==================================
    snprintf(payload, sizeof(payload), "%s{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"1\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim1_mno);
    if (now_sim_index == 1)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // =================== esim1 end ==================================

    // =================== esim2 start ==================================
    snprintf(payload, sizeof(payload), "%s,{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"2\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim2_mno);
    if (now_sim_index == 2)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // ==================== esim2 end ==================================

    // ==================== rsim start ==================================
#ifdef QRZL_HAVE_3_ESIM_CARD
    snprintf(payload, sizeof(payload), "%s,{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"0\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim3_mno);
    if (now_sim_index == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
 #else   
    if (now_sim_index == 0)
    {
        snprintf(payload, sizeof(payload), "%s,{", payload);
        snprintf(payload, sizeof(payload), "%s\"sim_id\":\"0\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_dynamic_data.imsi);
        snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
        snprintf(payload, sizeof(payload), "%s}", payload);
    }
    // ==================== rsim end ==================================
#endif
    snprintf(payload, sizeof(payload), "%s]", payload);
    // sim_info end 

    // system_info start
    snprintf(payload, sizeof(payload),
            "%s,\"system_info\":{", payload);

    double uptime_seconds = get_device_uptime();
    int hours = (int)(uptime_seconds / 3600);  // 转换为小时
    int minutes = (int)((uptime_seconds - (hours * 3600)) / 60);  // 剩余的分钟
    int seconds = (int)(uptime_seconds - (hours * 3600) - (minutes * 60));  // 剩余的秒数
    char sys_running_time[64] = {0};

    snprintf(sys_running_time, sizeof(sys_running_time), "%d:%d:%d", hours, minutes, seconds);
    snprintf(payload, sizeof(payload), "%s\"sys_running_time\":\"%s\"", payload, sys_running_time);
    char sys_current_time[64] = {0};

    get_local_time("%Y-%m-%d %H:%M:%S", sys_current_time, sizeof(sys_current_time));

    snprintf(payload, sizeof(payload), "%s,\"sys_current_time\":\"%s\"", payload, sys_current_time);
    snprintf(payload, sizeof(payload), "%s,\"sys_charge_status\":%d", payload, get_device_charge_status());
    snprintf(payload, sizeof(payload), "%s,\"sys_bat_level\":%s", payload, g_qrzl_device_dynamic_data.remain_power);
    snprintf(payload, sizeof(payload), "%s,\"sys_device_status\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn == 1 ? 0 : 1);
    
    snprintf(payload, sizeof(payload), "%s}", payload);
    // system_info end

    typedef struct {
        char mac[18];
        char ip[16];
    } ArpClient;
    
    ArpClient clients[32];
    int clients_nums = 0;

    FILE *arp_file;
    char arp_file_line[256] = {0};
    arp_file = fopen("/proc/net/arp", "r");
    if (arp_file == NULL) {
        qrzl_err("can't open /proc/net/arp file");
    } else {
        fgets(arp_file_line, sizeof(arp_file_line), arp_file);  // skip header

        while (fgets(arp_file_line, sizeof(arp_file_line), arp_file) != NULL) {
            char ip[16], mac[18], device[16], mask[16];
            unsigned int hw_type, flags;

            if (sscanf(arp_file_line, "%15s %x %x %17s %15s %15s", ip, &hw_type, &flags, mac, mask, device) != 6) {
                continue;
            }

            if (strlen(mac) == 17 && flags != 0x0) {
                strncpy(clients[clients_nums].mac, mac, sizeof(clients[clients_nums].mac));
                strncpy(clients[clients_nums].ip, ip, sizeof(clients[clients_nums].ip));
                clients_nums++;
            }
        }

        fclose(arp_file);
    }


    // hotspot_info start
    snprintf(payload, sizeof(payload),
            "%s,\"hotspot_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"hotspot_connected_num\":%d", payload, clients_nums);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_wps_status\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_hide\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_on\":\"%d\"", payload, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_name\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_connected_num_max\":%d", payload, g_qrzl_device_dynamic_data.max_access_num);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_encryption_mode\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_encryp_type);
    char gateway_ip[16] = {0};
    char lan_start_ip[16] = {0};
    char lan_end_ip[16] = {0};
    cfg_get_item("lan_ipaddr", gateway_ip, sizeof(gateway_ip));
    cfg_get_item("dhcpStart", lan_start_ip, sizeof(lan_start_ip));
    cfg_get_item("dhcpEnd", lan_end_ip, sizeof(lan_end_ip));

    snprintf(payload, sizeof(payload), "%s,\"hotspot_gateway_ip\":\"%s\"", payload, gateway_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_start_ip\":\"%s\"", payload, lan_start_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_end_ip\":\"%s\"", payload, lan_end_ip);
    
    // hotspot_connected_clients start
    snprintf(payload, sizeof(payload),
            "%s,\"hotspot_connected_clients\":[", payload);

    int i;
    for (i = 0; i < clients_nums; i++) {
        if (i != 0) {
            snprintf(payload, sizeof(payload), "%s,", payload);
        }
        snprintf(payload, sizeof(payload), "%s{\"mac\":\"%s\"", payload, clients[i].mac);
        snprintf(payload, sizeof(payload), "%s,\"ip\":\"%s\"", payload, clients[i].ip);
        snprintf(payload, sizeof(payload), "%s,\"name\":\"\"}", payload);
    }

    // FILE *arp_file;
    // char arp_file_line[256] = {0};
    // int clients_nums = 0;
    // char station_mac[512] = {0};
    // cfg_get_item("station_mac", station_mac, sizeof(station_mac));
    // // if (strlen(station_mac) == 0)
    // // {   // 如果连接的设备为空，就不需要再去读取arp文件，并且如果station_mac为空,strstr函数不会返回NULL
    // //     qrzl_log("station_mac is empty");
    // // } else {
    //     // 打开 /proc/net/arp 文件
    //     arp_file = fopen("/proc/net/arp", "r");
    //     if (arp_file == NULL) {
    //         qrzl_err("cann't open /proc/net/arp file");
    //     } else {
    //         // 跳过文件头部
    //         fgets(arp_file_line, sizeof(arp_file_line), arp_file);

    //         // 读取每一行，格式：IP address, HW address, Flags, etc.
    //         while (fgets(arp_file_line, sizeof(arp_file_line), arp_file) != NULL) {
    //             char ip[16];
    //             char mac[18];
    //             char device[16];
    //             char mask[16];
    //             unsigned int hw_type, flags;
                

    //             // 解析一行内容
    //             if (sscanf(arp_file_line, "%s %x %x %s %s %s", ip, &hw_type, &flags, mac, &mask, &device) != 6) {
    //                 continue; // 解析失败，跳过
    //             }
    //             // 过滤掉无效的条目（比如不具备有效MAC地址的条目）和 没有真正连接WiFi的人
    //             if (strlen(mac) == 17 && flags != "0x0") {
    //                 if (clients_nums != 0) {
    //                     snprintf(payload, sizeof(payload), "%s,", payload);
    //                 }
    //                 snprintf(payload, sizeof(payload), "%s{\"mac\":\"%s\"", payload, mac);
    //                 snprintf(payload, sizeof(payload), "%s,\"ip\":\"%s\"", payload, ip);
    //                 snprintf(payload, sizeof(payload), "%s,\"name\":\"\"}", payload);
    //                 clients_nums++;
    //             }
    //         }

    //         // 关闭文件
    //         fclose(arp_file);
    //     }
    // }

    snprintf(payload, sizeof(payload), "%s]", payload);
    // hotspot_connected_clients end
    snprintf(payload, sizeof(payload), "%s}", payload);
    // hotspot_info end

    // network_info start
    snprintf(payload, sizeof(payload),
            "%s,\"network_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"network_ip_addr\":\"%s\"", payload, g_qrzl_device_dynamic_data.current_wan_ip);
    snprintf(payload, sizeof(payload), "%s,\"network_strength_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_tx_bytes - ky_last_publish_tx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_download_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_rx_bytes - ky_last_publish_rx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_up_limit_net_speed() / 8));
    snprintf(payload, sizeof(payload), "%s,\"network_download_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_down_limit_net_speed() / 8));

    char auto_switch_esim_type[3] = {0};
	cfg_get_item("auto_switch_esim_type", auto_switch_esim_type, sizeof(auto_switch_esim_type));
    if (strcmp(auto_switch_esim_type, "1") == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"network_auto_slot\":1", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,\"network_auto_slot\":0", payload);
    }
    char sim_auth_mode[2] = {0};
    cfg_get_item("sim_auth_mode", sim_auth_mode, sizeof(sim_auth_mode));
    snprintf(payload, sizeof(payload), "%s,\"network_dual_sim\":%d", payload, strcmp("5", sim_auth_mode) == 0 ? 0 : 1);
    snprintf(payload, sizeof(payload), "%s}", payload);
    // network_info end

    // device_info start
    snprintf(payload, sizeof(payload),
            "%s,\"device_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"dev_imei1\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"dev_imei2\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"dev_sn\":\"%s\"", payload, g_qrzl_device_static_data.sn);
    snprintf(payload, sizeof(payload), "%s,\"dev_mac\":\"%s\"", payload, g_qrzl_device_static_data.mac);
    snprintf(payload, sizeof(payload), "%s,\"dev_sw_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload), "%s,\"dev_hw_version\":\"%s\"", payload, g_qrzl_device_static_data.hw_version);
    snprintf(payload, sizeof(payload), "%s,\"dev_mocor_sw_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload), "%s}", payload);
    // device_info end

    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        // 这里并不能说明真的被推送了
        qrzl_log("Message published!");
        ky_last_publish_rx_byte_flow = g_qrzl_device_dynamic_data.realtime_rx_bytes;
        ky_last_publish_tx_byte_flow = g_qrzl_device_dynamic_data.realtime_tx_bytes;
    }

    return 0;
}

int ky_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 60;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    char receive_topic[256] = {0};
    snprintf(receive_topic, sizeof(receive_topic), "mifiiot/%s/client", g_qrzl_device_static_data.sn);

    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}

int ky_cmd_handler(cJSON* j_cmd, MQTTClient* client_p)
{
    if (j_cmd == NULL || !cJSON_IsObject(j_cmd))
    {
        return -1;
    }
    cJSON *j_cmd_name = cJSON_GetObjectItem(j_cmd, "cmd_name");
    if (j_cmd_name == NULL || !cJSON_IsString(j_cmd_name))
    {
        return -1;
    }
    qrzl_log("start exec cmd_name: %s", j_cmd_name->valuestring);

    cJSON *j_cmd_params = cJSON_GetObjectItem(j_cmd, "cmd_params");
    if (strcmp("reboot", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_delay_ms = cJSON_GetObjectItem(j_cmd_params, "delay_ms");
            if (j_delay_ms != NULL && cJSON_IsNumber(j_delay_ms))
            {
                int delay_ms = j_delay_ms->valueint;
                if (delay_ms > 0)
                {
                    sleep_ms(delay_ms);
                }
            }
        }
        return restart_device();
    }

    else if (strcmp("power_off", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_delay_ms = cJSON_GetObjectItem(j_cmd_params, "delay_ms");
            if (j_delay_ms != NULL && cJSON_IsNumber(j_delay_ms))
            {
                int delay_ms = j_delay_ms->valueint;
                if (delay_ms > 0)
                {
                    sleep_ms(delay_ms);
                }
            }
        }
        return shutdown_device();
    }

    else if (strcmp("factory_reset", j_cmd_name->valuestring) == 0 || strcmp("recovery", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_delay_ms = cJSON_GetObjectItem(j_cmd_params, "delay_ms");
            if (j_delay_ms != NULL && cJSON_IsNumber(j_delay_ms))
            {
                int delay_ms = j_delay_ms->valueint;
                if (delay_ms > 0)
                {
                    sleep(delay_ms / 1000);
                }
            }
        }
        return reset_device();
    }

    else if (strcmp("device_enable", j_cmd_name->valuestring) == 0)
    {
        set_network_br0_disconnect(0);
        wifi_switch(1);
        limit_net_speed(0, 0);
        return 0;
    }

    else if (strcmp("device_disable", j_cmd_name->valuestring) == 0)
    {
        set_network_br0_disconnect(1);
        wifi_switch(0);
        limit_net_speed(10, 10);
        return 0;
    }

    else if (strcmp("upload_config_modify", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_dataupload_interval = cJSON_GetObjectItem(j_cmd_params, "dataupload_interval");
            if (j_dataupload_interval != NULL && cJSON_IsNumber(j_dataupload_interval))
            {
                int64_t dataupload_interval = j_dataupload_interval->valueint;
                if (dataupload_interval > 0)
                {
                    ky_publish_interval_ms = dataupload_interval;
                }
            }
        }
        return 0;
    }
 #if defined(QRZL_AUTH_ONE_LINK_HTTP) || defined(QRZL_AUTH_CMP_HTTP)
    else if(strcmp("authentic_switch", j_cmd_name->valuestring) == 0)
    {
        if(j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
           cJSON *j_value = cJSON_GetObjectItem(j_cmd_params, "value");
            if (j_value != NULL && cJSON_IsNumber(j_value))
            {
                int new_switch_value = j_value->valueint;  // 直接取整数
                char current_switch[32] = {0};

                // 获取当前的认证开关状态
                get_authentic_switch(current_switch, sizeof(current_switch));
                int current_value = atoi(current_switch);  // 转成整数方便比较

                qrzl_log("[认证开关] 请求: %d, 当前: %d", new_switch_value, current_value);

                // 验证输入值的合法性
                if (new_switch_value == 0) {
                    if (current_value != 0) {
                        set_authentic_switch("0");
                        qrzl_log("[认证开关] 已关闭二次认证功能");
                    } else {
                        qrzl_log("[认证开关] 二次认证已处于关闭状态");
                    }
                }
                else if (new_switch_value == 1) {
                    if (current_value != 1) {
                        set_authentic_switch("1");
                        qrzl_log("[认证开关] 已开启二次认证功能");
                    } else {
                        qrzl_log("[认证开关] 二次认证已处于开启状态");
                    }
                }
                else {
                    qrzl_log("[认证开关] 错误的值: %d", new_switch_value);
                }
            }
        }
        return 0;
    }
#endif

    else if (strcmp("shutdown_timeout_modify", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            // 这里的单位时是分钟
            cJSON* j_time = cJSON_GetObjectItem(j_cmd_params, "time");
            if (j_time != NULL && cJSON_IsNumber(j_time))
            {
                if (ky_shutdown_thread_id != 0) {
                    qrzl_log("cancel ky shutdown thread: %lu", ky_shutdown_thread_id);
                    pthread_cancel(ky_shutdown_thread_id);
                    ky_shutdown_thread_id = 0;
                }
                if (j_time->valueint > 0)
                {
                    if (pthread_create(&ky_shutdown_thread_id, NULL, ky_shutdown_thread, NULL) != 0) {
                        qrzl_err("Failed to create shutdown thread");
                    } else {
                        qrzl_log("create ky shutdown thread: %lu", ky_shutdown_thread_id);
                        ky_shutdown_time_s = j_time->valueint * 60;
                    }
                }
            }
        }
        return 0;
    }

    else if (strcmp("hotspot_on", j_cmd_name->valuestring) == 0
    || strcmp("hotspot_on_24g", j_cmd_name->valuestring) == 0)
    {
        wifi_switch(1);
        return 0;
    }

    else if (strcmp("hotspot_off", j_cmd_name->valuestring) == 0
    || strcmp("hotspot_off_24g", j_cmd_name->valuestring) == 0)
    {
        wifi_switch(0);
        return 0;
    }

    else if (strcmp("hotspot_modify", j_cmd_name->valuestring) == 0)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_hotspot_name = cJSON_GetObjectItem(j_cmd_params, "hotspot_name");
            cJSON* j_hotspot_password = cJSON_GetObjectItem(j_cmd_params, "hotspot_password");
            cJSON* j_hotspot_is_hide = cJSON_GetObjectItem(j_cmd_params, "hotspot_is_hide");
            cJSON* j_hotspot_connected_num_max = cJSON_GetObjectItem(j_cmd_params, "hotspot_connected_num_max");

            if (j_hotspot_name != NULL && cJSON_IsString(j_hotspot_name))
            {
                snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_hotspot_name->valuestring);
            }
            if (j_hotspot_password != NULL && cJSON_IsString(j_hotspot_password))
            {
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_hotspot_password->valuestring);
            }
            if (j_hotspot_is_hide != NULL && cJSON_IsNumber(j_hotspot_is_hide))
            {
                wifi_config.hide = j_hotspot_is_hide->valueint;
            }
            if (j_hotspot_connected_num_max != NULL && cJSON_IsNumber(j_hotspot_connected_num_max))
            {
                wifi_config.max_access_num = j_hotspot_connected_num_max->valueint;
            }
            update_wifi_by_config(&wifi_config);
        }
        return 0;
    }

    else if (strcmp("network_speed_limit", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_limit_type = cJSON_GetObjectItem(j_cmd_params, "type");
            cJSON* j_limit_unit = cJSON_GetObjectItem(j_cmd_params, "unit");
            cJSON* j_limit_value = cJSON_GetObjectItem(j_cmd_params, "value");
            if (j_limit_type != NULL && cJSON_IsString(j_limit_type)
                && j_limit_unit != NULL && cJSON_IsString(j_limit_unit)
                && j_limit_value != NULL && cJSON_IsString(j_limit_value))
            {
                
                uint64_t limit_value = atol(j_limit_value->valuestring);
                if (limit_value < 0)
                {
                    qrzl_log("limit_value is less than 0");
                    return -1;
                }

                if (strcmp("B", j_limit_unit->valuestring) == 0) {
                    limit_value = (limit_value * 8) / 1024;
                }
                else if (strcmp("KB", j_limit_unit->valuestring) == 0)
                {
                    limit_value = (limit_value * 8);
                }
                else if (strcmp("MB", j_limit_unit->valuestring) == 0)
                {
                    limit_value = (limit_value * 8 * 1024);
                }
                else if (strcmp("GB", j_limit_unit->valuestring) == 0)
                {
                    limit_value = (limit_value * 8 * 1024 * 1024);
                }
                else
                {
                    qrzl_log("error limit unit");
                    return -1;
                }
                

                if (strcmp("all", j_limit_type->valuestring) == 0)
                {
                    limit_net_speed(limit_value, limit_value);
                }
                else if (strcmp("upload", j_limit_type->valuestring) == 0)
                {
                    limit_net_speed(limit_value, 0);
                }
                else if (strcmp("download", j_limit_type->valuestring) == 0)
                {
                    limit_net_speed(0, limit_value);
                }
                else
                {
                    qrzl_log("error limit type");
                    return -1;
                }
            }
        }
        return 0;
    }

    else if (strcmp("network_info", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_network_auto_solt = cJSON_GetObjectItem(j_cmd_params, "network_auto_solt");
            if (j_network_auto_solt != NULL && cJSON_IsNumber(j_network_auto_solt))
            {
                qrzl_log("Kuyu --> 下发智能寻网开关:%d", j_network_auto_solt->valueint);
                if (j_network_auto_solt->valueint == 1)
                {
                    cfg_set("auto_switch_esim_type", "1");
                }
                else
                {
                    cfg_set("auto_switch_esim_type", "0");
                }
            }

            cJSON* j_network_dual_sim = cJSON_GetObjectItem(j_cmd_params, "network_dual_sim");
            if (j_network_dual_sim != NULL && cJSON_IsNumber(j_network_dual_sim))
            {
                if (j_network_dual_sim->valueint == 1)
                {
                    cfg_set("sim_auth_mode", "3");
                }
                else
                {
                    cfg_set("sim_auth_mode", "5");
                }
            }
        }
        return 0;
    }

    else if (strcmp("sim_switch", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_sim_id = cJSON_GetObjectItem(j_cmd_params, "sim_id");
            if (j_sim_id != NULL && cJSON_IsNumber(j_sim_id))
            {
                int sim_id = j_sim_id->valueint;
                // 酷鱼：0 外置卡； 1 卡槽2 ； 2 卡槽1
                qrzl_log("Kuyu 下发切卡指令 -->%d",sim_id);
                return switch_sim_card_not_restart(sim_id);
            }
        }
        return -1;
    }

    else if (strcmp("update_dev_info", j_cmd_name->valuestring) == 0)
    {
        return ky_publish_device_info(client_p);
    }

    else if (strcmp("update_dev_info", j_cmd_name->valuestring) == 0)
    {
        return ky_publish_device_info(client_p);
    }
    
    return 1;
}

int ky_order_msg_handler(cJSON* j_value, MQTTClient* client_p)
{
    cJSON *j_cmds = cJSON_GetObjectItem(j_value, "cmds");
    if (j_cmds == NULL || !cJSON_IsArray(j_cmds))
    {
        return -1;
    }
    int i;

    for (i = 0; i < cJSON_GetArraySize(j_cmds); i++)
    {
        cJSON *j_cmd = cJSON_GetArrayItem(j_cmds, i);
        ky_cmd_handler(j_cmd, client_p);
    }
    return 0;
}

int ky_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    cJSON *j_value = cJSON_Parse((char*)message->payload);
    if (j_value != NULL)
    {
        if (cJSON_IsObject(j_value))
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            ky_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        cJSON_Delete(j_value);
    }

    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void ky_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    ky_mqtt_connect(client_p);
    ky_publish_device_info(client_p);
}

void ky_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    // 这里才算真的被推送成功
    qrzl_log("Message with token %d delivered", dt);
}

void* ky_publish_loop(void* client_ptr) {
    qrzl_log("ky_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;
    uint32_t now_sleep_ms_total = 600 * 1000;
    uint32_t sleep_time_ms = 3 * 1000;
    int current_conn_num = 10; // 默认给10，这是为了后面判断方便
    char sta_count[3] = {0};
    int ret;

    while (1) {
        now_sleep_ms_total += sleep_time_ms;
#ifdef JCV_HW_MZ804_V1_4
        ret = cfg_get_item("sta_count", sta_count, 3);
        if (ret == 0) {
            current_conn_num = atoi(sta_count);
        }
        if (current_conn_num > 0) {
            if (now_sleep_ms_total >= ky_publish_interval_ms) {
                ky_publish_device_info(client_p);
                now_sleep_ms_total = 0;
            }
        } else {
            if (now_sleep_ms_total >= 600 * 1000) {
                ky_publish_device_info(client_p);
                now_sleep_ms_total = 0;
            }
        }
#else
    if (current_conn_num > 0) {
        if (now_sleep_ms_total >= ky_publish_interval_ms) {
            ky_publish_device_info(client_p);
            now_sleep_ms_total = 0;
        }
    }
#endif

        sleep_ms(sleep_time_ms);
    }
    return NULL;
}

static void ky_cloud_client_start()
{
    update_device_static_data();

    MQTTClient client;

    struct timeval tv;
    gettimeofday(&tv, NULL);
    char rand_str[10] = {0};
    generate_random_string(rand_str, sizeof(rand_str));
    char client_id[24] = {0};
    snprintf(client_id, sizeof(client_id), "%ld%ld%s", tv.tv_sec, tv.tv_usec, rand_str);

    MQTTClient_create(&client, mqtt_server, client_id, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, ky_mqtt_connlost, ky_message_arrived, ky_on_message_delivered);

    ky_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, ky_publish_loop, &client) != 0) {
        qrzl_err("Failed to create ky_publish_loop thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);

    return;
}

/* ========================================= end mqtt ky 类型的处理 ==================================================================== */
#endif /* QRZL_MQTT_CLIENT_KY */

/* ========================================= start mqtt yiming 类型的处理 ==================================================================== */
#ifdef QRZL_MQTT_CLIENT_YIMING

static uint64_t yiming_last_publish_rx_byte_flow = 0;
static uint64_t yiming_last_publish_tx_byte_flow = 0;
static int64_t yiming_publish_interval_ms = 300 * 1000;

// 移除mac地址的":"冒号
void remove_colons(const char *mac_with_colons, char *mac_clean, size_t len) {
    int j = 0, i = 0;
    for ( i ; mac_with_colons[i] != '\0' && j < len - 1; i++) {
        if (mac_with_colons[i] != ':') {
            mac_clean[j++] = mac_with_colons[i];
        }
    }
    mac_clean[j] = '\0';
}

int yiming_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    char hander_mac[32] = {0};
    remove_colons(g_qrzl_device_static_data.mac, hander_mac, sizeof(hander_mac));
    snprintf(topic, sizeof(topic), "public/device/out/%s", hander_mac);
    char payload[2048] = {0};
    int now_sim_index = get_device_current_sim_index_by_data();
    snprintf(payload, sizeof(payload), "{");

    snprintf(payload, sizeof(payload),
            "%s\"sw_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"sw_build_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"hw_version\":\"%s\"", payload, g_qrzl_device_static_data.hw_version);
    snprintf(payload, sizeof(payload),
            "%s,\"cur_sim_id\":\"%d\"", payload, now_sim_index);
    snprintf(payload, sizeof(payload),
            "%s,\"device_imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"device_mac\":\"%s\"", payload, g_qrzl_device_static_data.mac);
    // cur_sim_cato 不知道什么意思
    // snprintf(payload, sizeof(payload),
    //         "%s,\"cur_sim_cato\":\"%d\"", payload, -1);

    // sim_info start
    snprintf(payload, sizeof(payload),
            "%s,\"sim_info\":[", payload);

    int lte_rsrp = atoi(g_qrzl_device_dynamic_data.lte_rsrp);
    int sim_signal_level = 0;
    if (lte_rsrp >= -85) {
        sim_signal_level = 5;
    } else if (lte_rsrp >= -95)
    {
        sim_signal_level = 4;
    } else if (lte_rsrp >= -105)
    {
        sim_signal_level = 3;
    } else if (lte_rsrp >= -115)
    {
        sim_signal_level = 2;
    } else if (lte_rsrp >= -199)
    {
        sim_signal_level = 1;
    }
    
    

    // ================== esim1 start ==================================
    snprintf(payload, sizeof(payload), "%s{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"1\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim1_mno);
    if (now_sim_index == 1)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // =================== esim1 end ==================================

    // =================== esim2 start ==================================
    snprintf(payload, sizeof(payload), "%s,{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"2\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim2_mno);
    if (now_sim_index == 2)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // ==================== esim2 end ==================================

    // ==================== rsim start ==================================
    if (now_sim_index == 0)
    {
        snprintf(payload, sizeof(payload), "%s,{", payload);
        snprintf(payload, sizeof(payload), "%s\"sim_id\":\"0\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_dynamic_data.imsi);
        snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
        snprintf(payload, sizeof(payload), "%s}", payload);
    }
    // ==================== rsim end ==================================

    snprintf(payload, sizeof(payload), "%s]", payload);
    // sim_info end 

    // system_info start
    snprintf(payload, sizeof(payload),
            "%s,\"system_info\":{", payload);

    double uptime_seconds = get_device_uptime();
    int hours = (int)(uptime_seconds / 3600);  // 转换为小时
    int minutes = (int)((uptime_seconds - (hours * 3600)) / 60);  // 剩余的分钟
    int seconds = (int)(uptime_seconds - (hours * 3600) - (minutes * 60));  // 剩余的秒数
    char sys_running_time[64] = {0};

    snprintf(sys_running_time, sizeof(sys_running_time), "%d:%d:%d", hours, minutes, seconds);
    snprintf(payload, sizeof(payload), "%s\"sys_running_time\":\"%s\"", payload, sys_running_time);
    char sys_current_time[64] = {0};

    get_local_time("%Y-%m-%d %H:%M:%S", sys_current_time, sizeof(sys_current_time));

    snprintf(payload, sizeof(payload), "%s,\"sys_current_time\":\"%s\"", payload, sys_current_time);
    snprintf(payload, sizeof(payload), "%s,\"sys_charge_status\":%d", payload, get_device_charge_status());
    snprintf(payload, sizeof(payload), "%s,\"sys_board_temperature\":%s", payload, g_qrzl_device_dynamic_data.board_temperature);
    snprintf(payload, sizeof(payload), "%s,\"sys_bat_level\":%s", payload, g_qrzl_device_dynamic_data.remain_power);

    snprintf(payload, sizeof(payload), "%s}", payload);
    // system_info end

    typedef struct {
        char mac[18];
        char ip[16];
    } ArpClient;
    
    ArpClient clients[32];
    int clients_nums = 0;

    FILE *arp_file;
    char arp_file_line[256] = {0};
    arp_file = fopen("/proc/net/arp", "r");
    if (arp_file == NULL) {
        qrzl_err("can't open /proc/net/arp file");
    } else {
        fgets(arp_file_line, sizeof(arp_file_line), arp_file);  // skip header

        while (fgets(arp_file_line, sizeof(arp_file_line), arp_file) != NULL) {
            char ip[16], mac[18], device[16], mask[16];
            unsigned int hw_type, flags;

            if (sscanf(arp_file_line, "%15s %x %x %17s %15s %15s", ip, &hw_type, &flags, mac, mask, device) != 6) {
                continue;
            }

            if (strlen(mac) == 17 && flags != 0x0) {
                strncpy(clients[clients_nums].mac, mac, sizeof(clients[clients_nums].mac));
                strncpy(clients[clients_nums].ip, ip, sizeof(clients[clients_nums].ip));
                clients_nums++;
            }
        }
        clients_nums -= 1; // 移除本机的计数，看客户需要，这里要去除，因为前端web是不显示本机的
        fclose(arp_file);
    }


    // hotspot_info start
    snprintf(payload, sizeof(payload),
            "%s,\"hotspot_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"hotspot_connected_num\":%d", payload, clients_nums);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_wps_status\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_hide\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_on\":\"%d\"", payload, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_enable\":\"%d\"", payload, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_name\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_connected_num_max\":%d", payload, g_qrzl_device_dynamic_data.max_access_num);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_encryption_mode\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_encryp_type);
    char gateway_ip[16] = {0};
    char lan_start_ip[16] = {0};
    char lan_end_ip[16] = {0};
    cfg_get_item("lan_ipaddr", gateway_ip, sizeof(gateway_ip));
    cfg_get_item("dhcpStart", lan_start_ip, sizeof(lan_start_ip));
    cfg_get_item("dhcpEnd", lan_end_ip, sizeof(lan_end_ip));

    snprintf(payload, sizeof(payload), "%s,\"hotspot_gateway_ip\":\"%s\"", payload, gateway_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_start_ip\":\"%s\"", payload, lan_start_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_end_ip\":\"%s\"", payload, lan_end_ip);
    
    snprintf(payload, sizeof(payload), "%s}", payload);
    // hotspot_info end

    // network_info start
    snprintf(payload, sizeof(payload),
            "%s,\"network_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"network_ip_addr\":\"%s\"", payload, g_qrzl_device_dynamic_data.current_wan_ip);
    // 网络连接状态
    snprintf(payload, sizeof(payload), "%s,\"network_status\":\"%s\"", payload, get_current_net_status());
    snprintf(payload, sizeof(payload), "%s,\"network_strength_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_tx_bytes - yiming_last_publish_tx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_download_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_rx_bytes - yiming_last_publish_rx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_up_limit_net_speed() / 8));
    snprintf(payload, sizeof(payload), "%s,\"network_download_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_down_limit_net_speed() / 8));
    snprintf(payload, sizeof(payload), "%s}", payload);
    // network_info end

    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        // 这里并不能说明真的被推送了
        qrzl_log("Message published!");
        yiming_last_publish_rx_byte_flow = g_qrzl_device_dynamic_data.realtime_rx_bytes;
        yiming_last_publish_tx_byte_flow = g_qrzl_device_dynamic_data.realtime_tx_bytes;
    }

    return 0;
}

int yiming_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 60;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    char receive_topic[256] = {0};
    char hander_mac[32] = {0};
    remove_colons(g_qrzl_device_static_data.mac, hander_mac, sizeof(hander_mac));
    snprintf(receive_topic, sizeof(receive_topic), "public/device/in/%s", hander_mac);

    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    printf("Subscribed to topic: %s\n", receive_topic);
    return rc;
}

void* yiming_publish_loop(void* client_ptr) {
    qrzl_log("yiming_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;

    while (1) {
        yiming_publish_device_info(client_p);
        sleep_ms(yiming_publish_interval_ms);
    }
    return NULL;
}


int yiming_cmd_handler(cJSON* j_cmd, MQTTClient* client_p)
{
    if (j_cmd == NULL || !cJSON_IsObject(j_cmd))
    {
        return -1;
    }
    cJSON *j_cmd_name = cJSON_GetObjectItem(j_cmd, "cmd_name");
    if (j_cmd_name == NULL || !cJSON_IsString(j_cmd_name))
    {
        return -1;
    }
    qrzl_log("start exec cmd_name: %s", j_cmd_name->valuestring);

    cJSON *j_cmd_params = cJSON_GetObjectItem(j_cmd, "cmd_params");
    if (strcmp("reboot", j_cmd_name->valuestring) == 0)
    {
        return restart_device();
    }

    else if (strcmp("power_off", j_cmd_name->valuestring) == 0)
    {
        return shutdown_device();
    }

    else if (strcmp("recovery", j_cmd_name->valuestring) == 0)
    {
        return reset_device();
    }

    else if (strcmp("hotspot_on", j_cmd_name->valuestring) == 0)
    {
        return wifi_switch(1);
    }

    else if (strcmp("hotspot_off", j_cmd_name->valuestring) == 0)
    {
        return wifi_switch(0);
    }

    else if (strcmp("hotspot_modify", j_cmd_name->valuestring) == 0)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_hotspot_name = cJSON_GetObjectItem(j_cmd_params, "hotspot_name");
            cJSON* j_hotspot_password = cJSON_GetObjectItem(j_cmd_params, "hotspot_password");
            cJSON* j_hotspot_is_hide = cJSON_GetObjectItem(j_cmd_params, "hotspot_is_hide");
            cJSON* j_hotspot_connected_num_max = cJSON_GetObjectItem(j_cmd_params, "hotspot_connected_num_max");

            if (j_hotspot_name != NULL && cJSON_IsString(j_hotspot_name))
            {
                snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_hotspot_name->valuestring);
            }
            if (j_hotspot_password != NULL && cJSON_IsString(j_hotspot_password))
            {
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_hotspot_password->valuestring);
            }
            if (j_hotspot_is_hide != NULL && cJSON_IsNumber(j_hotspot_is_hide))
            {
                wifi_config.hide = j_hotspot_is_hide->valueint;
            }
            if (j_hotspot_connected_num_max != NULL && cJSON_IsNumber(j_hotspot_connected_num_max))
            {
                wifi_config.max_access_num = j_hotspot_connected_num_max->valueint;
            }
            update_wifi_by_config(&wifi_config);
        }
        return 0;
    }

    else if (strcmp("network_speed_limit", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_limit_type = cJSON_GetObjectItem(j_cmd_params, "type");
            cJSON* j_limit_unit = cJSON_GetObjectItem(j_cmd_params, "unit");
            cJSON* j_limit_value = cJSON_GetObjectItem(j_cmd_params, "value");
            if (j_limit_type != NULL && cJSON_IsString(j_limit_type)
                && j_limit_unit != NULL && cJSON_IsString(j_limit_unit)
                && j_limit_value != NULL && cJSON_IsNumber(j_limit_value))
            {
                
                uint64_t limit_value = (uint64_t) j_limit_value->valueint;
                if (limit_value < 0)
                {
                    qrzl_log("limit_value is less than 0");
                    return -1;
                }

                if (strcmp("B", j_limit_unit->valuestring) == 0) {
                    limit_value = (limit_value * 8) / 1024;
                }
                else if (strcmp("KB", j_limit_unit->valuestring) == 0)
                {
                    limit_value = (limit_value * 8);
                }
                else if (strcmp("MB", j_limit_unit->valuestring) == 0)
                {
                    limit_value = (limit_value * 8 * 1024);
                }
                else if (strcmp("GB", j_limit_unit->valuestring) == 0)
                {
                    limit_value = (limit_value * 8 * 1024 * 1024);
                }
                else
                {
                    qrzl_log("error limit unit");
                    return -1;
                }
                

                if (strcmp("all", j_limit_type->valuestring) == 0)
                {
                    limit_net_speed(limit_value, limit_value);
                }
                else if (strcmp("upload", j_limit_type->valuestring) == 0)
                {
                    limit_net_speed(limit_value, 0);
                }
                else if (strcmp("download", j_limit_type->valuestring) == 0)
                {
                    limit_net_speed(0, limit_value);
                }
                else
                {
                    qrzl_log("error limit type");
                    return -1;
                }
            }
        }
        return 0;
    }

    else if (strcmp("sim_switch", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_sim_id = cJSON_GetObjectItem(j_cmd_params, "sim_id");
            if (j_sim_id != NULL && cJSON_IsNumber(j_sim_id))
            {
                int sim_id = j_sim_id->valueint;
                if (sim_id > 2) {
                    qrzl_log("yiming sim_id --> %d , 不可用。",sim_id);
                    return -1;
                }
                if (sim_id == 1 && g_qrzl_device_dynamic_data.slot_esim1_is_enable != 1) {
                    qrzl_log("yiming sim_id --> %d , ESIM1 卡槽不可用。",sim_id);
                    return -1;
                } else if (sim_id == 2 && g_qrzl_device_dynamic_data.slot_esim2_is_enable != 1) {
                    qrzl_log("yiming sim_id --> %d , ESIM2 卡槽不可用。",sim_id);
                    return -1;
                } else if (sim_id == 0 && g_qrzl_device_dynamic_data.slot_esim3_is_enable != 1) {
                    qrzl_log("yiming sim_id --> %d , 外置 卡槽不可用。",sim_id);
                    return -1;
                }
                // 0 外置卡； 1 卡槽1 ； 2 卡槽2
                qrzl_log("yiming 下发切卡指令 -->%d",sim_id);
                return switch_sim_card_not_restart(sim_id);
            }
        }
        return -1;
    }

    else if (strcmp("device_enable", j_cmd_name->valuestring) == 0)
    {
        set_network_br0_disconnect(0);
        limit_net_speed(0, 0);
        return 0;
    }

    else if (strcmp("device_disable", j_cmd_name->valuestring) == 0)
    {
        set_network_br0_disconnect(1);
        limit_net_speed(10, 10);
        return 0;
    }

    else if (strcmp("upload_config_modify", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_dataupload_interval = cJSON_GetObjectItem(j_cmd_params, "dataupload_interval");
            if (j_dataupload_interval != NULL && cJSON_IsNumber(j_dataupload_interval))
            {
                int64_t dataupload_interval = j_dataupload_interval->valueint;
                if (dataupload_interval > 0)
                {
                    yiming_publish_interval_ms = dataupload_interval;
                    qrzl_log("yiming_publish_interval_ms now is %d", yiming_publish_interval_ms);
                }
            }
        }
        return 0;
    }

    // 启用或禁用卡槽
    else if (strcmp("enable_disable_slots", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_sim_ids = cJSON_GetObjectItem(j_cmd_params, "sim_ids");
            cJSON* j_sim_disable = cJSON_GetObjectItem(j_cmd_params, "disable");
            if(j_sim_ids != NULL && cJSON_IsArray(j_sim_ids) && j_sim_disable != NULL && cJSON_IsNumber(j_sim_disable)) {
                int i;
                for (i = 0 ; i < cJSON_GetArraySize(j_sim_ids); i++)
                {
                    cJSON* item = cJSON_GetArrayItem(j_sim_ids, i);
                    if (item && cJSON_IsString(item)) {
                        qrzl_log("sim_ids-> i = %d, sim_id = %s", i, item->valuestring);
                        set_slot_state(j_sim_disable->valueint, atoi(item->valuestring));
                    } else {
                        qrzl_log("sim_ids->%d is not a string or NULL", i);
                        return 0;
                    }
                }
            }
        }
        return 0;
    }

    // 隐藏或显示卡槽
    else if (strcmp("enable_hidden_slots", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_sim_id = cJSON_GetObjectItem(j_cmd_params, "sim_id");
            cJSON* j_sim_hidden = cJSON_GetObjectItem(j_cmd_params, "hidden");
            if (j_sim_id != NULL && cJSON_IsNumber(j_sim_id) && j_sim_hidden != NULL && cJSON_IsNumber(j_sim_hidden)) {
                char sim_list_str[40] = {0};
                cfg_get_item("sim_select_num_type", sim_list_str, sizeof(sim_list_str));
                char sim_str[15] = {0};
                switch (j_sim_id->valueint)
                {
                    case 1:
                        snprintf(sim_str, sizeof(sim_str), "ESIM1_only");
                        break;
                    case 2:
                        snprintf(sim_str, sizeof(sim_str), "ESIM2_only");
                        break;
                    case 0:
                        snprintf(sim_str, sizeof(sim_str), "RSIM_only");
                        break;
                    default:
                        break;
                }
                if (j_sim_hidden->valueint == 0) {
                    // 隐藏卡槽
                    if(contains_type(sim_list_str, sim_str) == 1) {
                        qrzl_log("hidden slot %s ", sim_str);
                        remove_item_from_csv(sim_list_str, sim_str);
                    }
                } else if(j_sim_hidden->valueint == 1) {
                    // 显示卡槽
                    if(contains_type(sim_list_str, sim_str) != 1) {
                        qrzl_log("show slot %s ", sim_str);
                        add_item_to_csv(sim_list_str, sim_str);
                    }
                }
                // 设置ESIM列表
                cfg_set("sim_select_num_type", sim_list_str);
            }
            return 0;
        }
        return 0;
    }

    else if (strcmp("netmode_switch", j_cmd_name->valuestring) == 0)
    {
        if (j_cmd_params != NULL && cJSON_IsObject(j_cmd_params))
        {
            cJSON* j_net_mode = cJSON_GetObjectItem(j_cmd_params, "netmode");
            if (j_net_mode != NULL && cJSON_IsNumber(j_net_mode)) {
                if (j_net_mode->valueint == 4) {
                    qrzl_log("netmode_switch-> 4G");
                    // 目前只有4G
                } else if (j_net_mode->valueint == 5) {
                    qrzl_log("netmode_switch-> 5G");
                } else if (j_net_mode->valueint == 6) {
                    qrzl_log("netmode_switch-> 4G/5G");
                }
            }
            return 0;
        }
        return 0;
    }

    else if (strcmp("update_dev_info", j_cmd_name->valuestring) == 0)
    {
        return yiming_publish_device_info(client_p);
    }
    
    return 1;
}


int yiming_order_msg_handler(cJSON* j_value, MQTTClient* client_p)
{
    cJSON *j_cmds = cJSON_GetObjectItem(j_value, "cmds");
    if (j_cmds == NULL || !cJSON_IsArray(j_cmds))
    {
        return -1;
    }
    int i;

    for (i = 0; i < cJSON_GetArraySize(j_cmds); i++)
    {
        cJSON *j_cmd = cJSON_GetArrayItem(j_cmds, i);
        yiming_cmd_handler(j_cmd, client_p);
    }
    return 0;
}

int yiming_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    cJSON *j_value = cJSON_Parse((char*)message->payload);
    if (j_value != NULL)
    {
        if (cJSON_IsObject(j_value))
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            yiming_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        cJSON_Delete(j_value);
    }

    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void yiming_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    yiming_mqtt_connect(client_p);
    yiming_publish_device_info(client_p);
}

void yiming_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    // 这里才算真的被推送成功
    qrzl_log("Message with token %d delivered", dt);
}

static void yiming_cloud_client_start()
{
    update_device_static_data();

    MQTTClient client;

    MQTTClient_create(&client, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, yiming_mqtt_connlost, yiming_message_arrived, yiming_on_message_delivered);

    yiming_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, yiming_publish_loop, &client) != 0) {
        qrzl_err("Failed to create yiming_publish_loop thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);

    return;
}

/* ========================================= end mqtt yiming 类型的处理 ==================================================================== */
#endif /* QRZL_MQTT_CLIENT_YIMING */

/* ========================================= start mqtt wuxing 类型的处理 ==================================================================== */
#ifdef QRZL_MQTT_CLIENT_WUXING
#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define MAX_MAC_LIST_LEN 512
static int64_t wuxing_publish_interval_ms = 300 * 1000;
// 全局 NV 标志，表示当前是否使用新服务器
static int wuxing_new_server_flag = 0;
static int new_server_fail_count = 0;
char mac_list[MAX_MAC_LIST_LEN] = {0};
//下面数组保存的是最近一次指令下发的MQTT更改信息，连接新地址要用
static char wuxing_new_mqtt_server[128] = {0};
static char wuxing_new_mqtt_username[128] = {0};
static char wuxing_new_mqtt_password[128] = {0};
int wuxing_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message);
void wuxing_mqtt_connlost(void *context, char *cause);
void wuxing_on_message_delivered(void* context, MQTTClient_deliveryToken dt);
static void *wuxing_mqtt_update_server(void *arg);
static void wuxing_revert_to_factory(MQTTClient *client_p);
static void timestamp_to_string(time_t timestamp, char *buffer, size_t buffer_size)
{
    // 将时间戳转换为 tm 结构
    struct tm *time_info = localtime(&timestamp);

    // 格式化时间为 yyyymmddhhmmss
    strftime(buffer, buffer_size, "%Y%m%d%H%M%S", time_info);
}
static void set_wuxing_new_server_flag(int flag)
{
    wuxing_new_server_flag = flag ? 1 : 0;      // 内存标志
    cfg_set("wuxing_new_server_flag", flag ? "1" : "0");  // 写 NV
    if (!flag) {
        new_server_fail_count = 0;             // 回退时清零失败计数
    }
}
// 字节转 MB
static inline double bytes_to_mb(uint64_t bytes)
{
    return bytes / (1024.0 * 1024.0);
}
static size_t wuxing_http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}
static int wuxing_https_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, wuxing_http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}
// 获取当前连接WIFI用户的设备MAC信息，并保存到全局mac_list里面
static void wuxing_get_connet_device_macinfo()
{   
    //每次进来先把全局mac_list清空
    memset(mac_list, 0, sizeof(mac_list));
    int offset = 0;
    // 请求地址
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    // 接收响应
    char rev_response[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 获取网关
    char lan_addr[20] = {0};
    cfg_get_item("lan_ipaddr", lan_addr, sizeof(lan_addr));

    // 登陆 http://*************/reqproc/proc_post?goformId=ALK_LOGIN
    snprintf(request_url, sizeof(request_url), "http://%s/reqproc/proc_post?goformId=ALK_LOGIN", lan_addr);
    int login_res = wuxing_https_send_get_request(request_url, rev_response);
    if(login_res != 0) {
        qrzl_log("ALK_LOGIN 登陆失败");
        return NULL;
    } else {
        qrzl_log("ALK_LOGIN 登陆成功");
    }

    // 获取当前连接的设备信息 http://*************/reqproc/proc_get?cmd=station_list
    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "http://%s/reqproc/proc_get?cmd=station_list", lan_addr);
    // 清空rev_response
    memset(rev_response, 0, sizeof(rev_response));
    int sta_list_res = wuxing_https_send_get_request(request_url, rev_response);
    if(sta_list_res != 0) {
        qrzl_log("当前连接的设备信息 失败");
        return NULL;
    }

    // 解析 JSON
    cJSON* root = cJSON_Parse((char*)rev_response);
    if (!root) {
        qrzl_log("root JSON 解析失败");
        return NULL;
    }
    cJSON* station_list_arry = cJSON_GetObjectItem(root, "station_list");
    if (station_list_arry != NULL && cJSON_IsArray(station_list_arry))
    {
        qrzl_log("有 station_list,长度为: %d", cJSON_GetArraySize(station_list_arry));
        int i;
        for (i = 0; i < cJSON_GetArraySize(station_list_arry); i++)
        {
            cJSON *j_station = cJSON_GetArrayItem(station_list_arry, i);
            if (j_station == NULL)
            {
                continue;
            }

            cJSON *j_mac = cJSON_GetObjectItem(j_station, "mac_addr");
            if (j_mac != NULL && cJSON_IsString(j_mac))
            {
                qrzl_log("第 %d 个设备 mac_addr = %s", i, j_mac->valuestring);

                // 拼接到 mac_list
                int n = snprintf(mac_list + offset,
                                MAX_MAC_LIST_LEN - offset,
                                "%s%s",
                                (offset > 0 ? "," : ""),  // 如果不是第一个，加逗号
                                j_mac->valuestring);
                if (n < 0 || n >= MAX_MAC_LIST_LEN - offset)
                {
                    qrzl_log("mac_list 缓冲区不够，停止拼接");
                    break;
                }
                offset += n;
            }
        }
        qrzl_log("拼接后的 mac_list: %s", mac_list);
    }
    else
    {
        qrzl_log("没有 station_list 或不是数组");
    }
    cJSON_Delete(root);
}
//将出厂设置的MQTT信息赋给运行时的配置
static void get_factory_MQTT_info()
{
    cfg_get_item("qrzl_cloud_mqtt_server", mqtt_server, sizeof(mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_username", mqtt_username, sizeof(mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_password", mqtt_password, sizeof(mqtt_password));
}
int wuxing_publish_command_result(MQTTClient* client_p,const char *cmd, int result,const char * uuid)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    snprintf(topic, sizeof(topic), "wxiot/up/3/%s/result", g_qrzl_device_static_data.imei);
    char payload[1024] = {0};
    // 组装 JSON
    snprintf(payload, sizeof(payload), "{");
    snprintf(payload, sizeof(payload),
            "%s\"cId\":\"3\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"dId\":\"%s\"", payload,g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"cmd\":\"%s\"", payload, cmd);
    if(strcmp(cmd,"macList") == 0)
    {
        wuxing_get_connet_device_macinfo();
        snprintf(payload, sizeof(payload),
                "%s,\"o\":\"%s\"", payload, mac_list);
    }
    snprintf(payload, sizeof(payload),
            "%s,\"result\":\"%d\"", payload, result);
    snprintf(payload, sizeof(payload),
            "%s,\"uuid\":\"%s\"", payload,uuid);
    snprintf(payload, sizeof(payload), "%s}", payload);
    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        //这里并不能说明真的推送了
        qrzl_log("指令结果上报完成!");
    }
    return 0;
}
int wuxing_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    snprintf(topic, sizeof(topic), "wxiot/up/3/recinfo");
    char payload[2048] = {0};
    int now_sim_index = get_device_current_sim_index_by_data();
    snprintf(payload, sizeof(payload), "{");
    //CID暂时没给出来，随便填一个，dID同理
    snprintf(payload, sizeof(payload),
            "%s\"cId\":\"3\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"dId\":\"%s\"", payload,g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
    snprintf(payload, sizeof(payload),
            "%s,\"us\":\"%.2f\"", payload,g_qrzl_device_dynamic_data.up_speed_bps / 1000000.0);//Mbps
    snprintf(payload, sizeof(payload),
            "%s,\"ds\":\"%.2f\"", payload,g_qrzl_device_dynamic_data.down_speed_bps / 1000000.0);

    uint64_t speedLimitUp = get_up_limit_net_speed();
    uint64_t speedLimitDown = get_down_limit_net_speed();
    snprintf(payload, sizeof(payload),
            "%s,\"speedLimitUp\":\"%llu\"", payload, speedLimitUp * 1000 / 8);//byte
    snprintf(payload, sizeof(payload),
            "%s,\"speedLimitDown\":\"%llu\"", payload, speedLimitDown * 1000 / 8);//byte
    snprintf(payload, sizeof(payload),
            "%s,\"rsrp\":\"%s\"", payload, g_qrzl_device_dynamic_data.lte_rsrp);
    int main_sim = 0;
#ifdef QRZL_QICHENG_SWITCHCARD_ZERO_IS_OUTCARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 1;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 2;
    } else {
        main_sim = 0;
    }
#else
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 0;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 1;
    } else {
        main_sim = 2;
    }
#endif
    //这个值不具有实时性
    snprintf(payload, sizeof(payload),
            "%s,\"mainSim\":\"%d\"", payload, main_sim);
    snprintf(payload, sizeof(payload),
            "%s,\"dualSim\":\"1\"", payload);
    int wifistatus = 1;
    if (g_qrzl_device_dynamic_data.wifi_enable == 0) {
        wifistatus = 0;
    } else if (g_qrzl_device_dynamic_data.wifi_enable == 1 && g_qrzl_device_dynamic_data.wifi_hide == 1)
    {
        wifistatus = 2;
    }
    snprintf(payload, sizeof(payload),
            "%s,\"wifiStatus\":\"%d\"", payload, wifistatus);
    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    time_t current_time = time(NULL);
    timestamp_to_string(current_time, time_string, sizeof(time_string));
    snprintf(payload, sizeof(payload),
            "%s,\"currentTime\":\"%s\"", payload, time_string);
    snprintf(payload, sizeof(payload),
            "%s,\"tem\":\"%s\"", payload, g_qrzl_device_dynamic_data.board_temperature);
    snprintf(payload, sizeof(payload),
            "%s,\"mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    snprintf(payload, sizeof(payload),
            "%s,\"lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
    snprintf(payload, sizeof(payload),
            "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload),
            "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key_base64);
    snprintf(payload, sizeof(payload),
            "%s,\"connectedNum\":\"%d\"", payload, g_qrzl_device_dynamic_data.conn_num);
    snprintf(payload, sizeof(payload),
            "%s,\"softwareVersion\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"deviceModel\":\"%s\"", payload, g_qrzl_device_static_data.device_type);
    snprintf(payload, sizeof(payload),
            "%s,\"macAddress\":\"%s\"", payload, g_qrzl_device_static_data.mac);
    EsimFluxStat esim_fluxstat = get_esim_fluxstat();
    snprintf(payload, sizeof(payload),
            "%s,\"card1DailyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim1_flux_day_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card1MonthlyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim1_flux_month_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card2DailyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim2_flux_day_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card2MonthlyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim2_flux_month_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card3DailyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.rsim_flux_day_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card3MonthlyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.rsim_flux_month_total));
    char networkStatus[2]={0};
    int ret=cfg_get_item("qrzl_user_net_disconn", networkStatus, sizeof(networkStatus));
    if (ret != 0)  // 没获取到
    {
        snprintf(networkStatus, sizeof(networkStatus), "0");
    }   
    snprintf(payload, sizeof(payload),
            "%s,\"networkStatus\":\"%d\"", payload, !atoi(networkStatus));
    snprintf(payload, sizeof(payload),
            "%s,\"sn\":\"%s\"", payload, g_qrzl_device_static_data.sn);
    snprintf(payload, sizeof(payload),
            "%s,\"battery\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);
    int jugdeTelecom=get_current_isp();
    snprintf(payload, sizeof(payload),
            "%s,\"switchCustomTelecom\":\"%d\"", payload, jugdeTelecom == 3? 0 : 1);
    snprintf(payload, sizeof(payload),"%s,\"o\":\"\"", payload);//预留的
    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        //这里并不能说明真的推送了
        qrzl_log("Message published!");
    }
    return 0;
}
int wuxing_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    if (wuxing_new_server_flag) {
        conn_opts.username = wuxing_new_mqtt_username;
        conn_opts.password = wuxing_new_mqtt_password;
    } else {
        conn_opts.username = mqtt_username;
        conn_opts.password = mqtt_password;
    }

    conn_opts.keepAliveInterval = 15;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        qrzl_log("MQTT connect username:%s,password:%s failed", conn_opts.username, conn_opts.password);
        sleep(10);
        //如果地址不是最初始的地址,就设置一个次数
        if(wuxing_new_server_flag == 1)
        {
            new_server_fail_count++;
            if(new_server_fail_count >= 3)
            {
                pthread_t thread_time_out;
                pthread_create(&thread_time_out, NULL, wuxing_revert_to_factory, (void *)client_p);
                pthread_detach(thread_time_out);  // 让线程自动回收
                return -1; // 直接退出当前连接流程
            }
        }
    }

    char receive_topic[256] = {0};
    snprintf(receive_topic, sizeof(receive_topic), "wxiot/down/3/%s", g_qrzl_device_static_data.imei);
    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}
void* wuxing_publish_loop(void* client_ptr) {
    qrzl_log("wuxing_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;

    while (1) {
        wuxing_publish_device_info(client_p);
        sleep_ms(wuxing_publish_interval_ms);
    }
    return NULL;
}
static void wuxing_revert_to_factory(MQTTClient *client_p) {
    sleep(10);
    qrzl_log("回退MQTT到出厂版本...");
    get_factory_MQTT_info();
    //进到回退这里认为在旧地址
    set_wuxing_new_server_flag(0);
    new_server_fail_count = 0;

    // 断开并销毁旧连接
    MQTTClient_disconnect(*client_p, 0);
    MQTTClient_destroy(client_p);
    qrzl_log("reconnect target :%s", mqtt_server);
    // 重建客户端
    MQTTClient_create(client_p, mqtt_server, g_qrzl_device_static_data.imei,
                      MQTTCLIENT_PERSISTENCE_NONE, NULL);
    MQTTClient_setCallbacks(*client_p, client_p, 
                            wuxing_mqtt_connlost, 
                            wuxing_message_arrived, 
                            wuxing_on_message_delivered);

    // 重新连接
    wuxing_mqtt_connect(client_p);
    wuxing_publish_device_info(client_p);
}
int wuxing_cmd_handler(cJSON* j_value, MQTTClient* client_p)
{
    int ret;
    cJSON *j_uuid = cJSON_GetObjectItem(j_value, "uuid");
    
    cJSON* j_switch = cJSON_GetObjectItem(j_value, "simSwitch");
    if (j_switch != NULL && cJSON_IsString(j_switch) && j_switch->valuestring)
    {
        int switch_crad_count = 0;
        while (g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
        {
            qrzl_log("正在测网，不能切卡");
            sleep(5);
            switch_crad_count++;
        }
        
        int ret;
        int tmp = atoi(j_switch->valuestring);
        if (tmp == 0)
        {
            ret = switch_sim_card_not_restart(1);
        }
        else if (tmp == 1)
        {
            ret = switch_sim_card_not_restart(2);
        }
        else if (tmp == 2)
        {
            ret = switch_sim_card_not_restart(0);
        }
        
        if (tmp > 0 && tmp < 4 && ret == 0)
        {
            // 暂时不需要做任何操作
        }
        wuxing_publish_command_result(client_p, "switch_sim_card", 1, j_uuid->valuestring);
    }
    
    cJSON *j_ssid = cJSON_GetObjectItem(j_value, "ssid");
    if (j_ssid != NULL && cJSON_IsString(j_ssid) && j_ssid->valuestring)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid->valuestring);
        update_wifi_by_config(&wifi_config);
        if(strlen(j_ssid->valuestring) > 0)
        {
            wuxing_publish_command_result(client_p, "ssid", 1, j_uuid->valuestring);
        }else {
            wuxing_publish_command_result(client_p, "ssid", 0, j_uuid->valuestring);
        }
    }
    cJSON *j_password = cJSON_GetObjectItem(j_value, "password");
    if (j_password != NULL && cJSON_IsString(j_password) )
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_password->valuestring);
        update_wifi_by_config(&wifi_config);
        if(strlen(j_password->valuestring) >= 8 )
        {
            wuxing_publish_command_result(client_p, "password", 1, j_uuid->valuestring);
        }else {
            wuxing_publish_command_result(client_p, "password", 0, j_uuid->valuestring);
        }
    }
    cJSON *j_hidden = cJSON_GetObjectItem(j_value, "hidden");
    if (j_hidden != NULL && cJSON_IsString(j_hidden) )
    {   int flag = 0;
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if(strcmp(j_hidden->valuestring, "0") == 0){
            flag = 1;
            wifi_config.enable = 0;
        }else if (strcmp(j_hidden->valuestring, "1") == 0)
        {   flag = 1;
            wifi_config.enable = 1;
            wifi_config.hide = 0;
        }else if (strcmp(j_hidden->valuestring, "2") == 0)
        {   flag = 1;
            wifi_config.enable = 1;
            wifi_config.hide = 1;
        }
        update_wifi_by_config(&wifi_config);
        wuxing_publish_command_result(client_p, "hidden", flag , j_uuid->valuestring);
    }
    cJSON *j_reboot = cJSON_GetObjectItem(j_value, "reboot");
    if (j_reboot != NULL && cJSON_IsString(j_reboot) )
    {
        if (strcmp(j_reboot->valuestring, "1") == 0)
        {
            qrzl_log("强制重启设备");
            ret = restart_device();
        }
    }
    cJSON *j_speedDownLink = cJSON_GetObjectItem(j_value, "speedDownLink");
    if (j_speedDownLink != NULL && cJSON_IsString(j_speedDownLink) )
    {
        uint64_t limit_speed=atoi(j_speedDownLink->valuestring);
        uint64_t up_limit_speed=get_up_limit_net_speed();
        limit_net_speed(up_limit_speed, limit_speed * 8 /1000);
        wuxing_publish_command_result(client_p, "speedDownLink", 1, j_uuid->valuestring);
    }
    cJSON *j_speedUpLink = cJSON_GetObjectItem(j_value, "speedUpLink");
    if (j_speedUpLink != NULL && cJSON_IsString(j_speedUpLink) )
    {
        uint64_t limit_speed=atoi(j_speedUpLink->valuestring);
        uint64_t down_limit_speed=get_down_limit_net_speed();
        limit_net_speed(down_limit_speed, 0);
        wuxing_publish_command_result(client_p, "speedUpLink", 1, j_uuid->valuestring);
    }
    cJSON *j_nextRptTime = cJSON_GetObjectItem(j_value, "nextRptTime");
    if (j_nextRptTime != NULL && cJSON_IsString(j_nextRptTime) )
    {   int flag = 0;
        int interval_time = atoi(j_nextRptTime->valuestring);
        if(0 < interval_time)
        {   flag = 1;
            wuxing_publish_interval_ms = interval_time * 1000;
        }
        wuxing_publish_command_result(client_p, "nextRptTime", flag , j_uuid->valuestring);
    }
    cJSON *j_firmwareUrl = cJSON_GetObjectItem(j_value, "firmwareUrl");
    if (j_firmwareUrl != NULL && cJSON_IsString(j_firmwareUrl) )
    {   
        //不做处理
        qrzl_log("ota_test");
        wuxing_publish_command_result(client_p, "firmwareUrl", 0 , j_uuid->valuestring);
    }
    cJSON* j_maxConnection = cJSON_GetObjectItem(j_value, "maxConnection");
    if (j_maxConnection != NULL && cJSON_IsString(j_maxConnection) && j_maxConnection->valuestring)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        wifi_config.max_access_num = atoi(j_maxConnection->valuestring);
        update_wifi_by_config(&wifi_config);
        wuxing_publish_command_result(client_p, "maxConnection", 1, j_uuid->valuestring);
    }
    cJSON* j_resetDevice = cJSON_GetObjectItem(j_value, "resetDevice");
    if (j_resetDevice != NULL && cJSON_IsString(j_resetDevice) && j_resetDevice->valuestring)
    {
        if(atoi(j_resetDevice->valuestring))
        {
            qrzl_log("强制重置设备");
            reset_device();
        }
    }
    cJSON* j_pseudoDisnetwork = cJSON_GetObjectItem(j_value, "pseudoDisnetwork");
    if (j_pseudoDisnetwork != NULL && cJSON_IsString(j_pseudoDisnetwork) && j_pseudoDisnetwork->valuestring)
    {
        if(atoi(j_pseudoDisnetwork->valuestring) == 1)
        {
            qrzl_log("强制断开网络");
            set_network_br0_disconnect(1);
            wuxing_publish_command_result(client_p, "pseudoDisnetwork", 1, j_uuid->valuestring);
        }
        else if(atoi(j_pseudoDisnetwork->valuestring) == 0)
        {
            qrzl_log("恢复网络连接");
            set_network_br0_disconnect(0);
            wuxing_publish_command_result(client_p, "pseudoDisnetwork", 1, j_uuid->valuestring);
        }
        else
        {
            qrzl_log("pseudoDisnetwork值无效");
            wuxing_publish_command_result(client_p, "pseudoDisnetwork", 0, j_uuid->valuestring);
        }
    }
    cJSON* j_auth_switch = cJSON_GetObjectItem(j_value, "auth_switch");
    if (j_auth_switch != NULL && cJSON_IsString(j_auth_switch))
    {
        const char *new_switch_value = j_auth_switch->valuestring;
        char current_switch[32] = {0};
        // 获取当前的认证开关状态
        get_authentic_switch(current_switch, sizeof(current_switch));
        qrzl_log("[认证开关] 请求: %s, 当前: %s", new_switch_value, current_switch);
        // 验证输入值的合法性
        if (strcmp(new_switch_value, "0") == 0) {
            if (strcmp(current_switch, "0") != 0) {
                set_authentic_switch("0");
                qrzl_log("[认证开关] 已关闭二次认证功能");
                wuxing_publish_command_result(client_p, "auth_switch", 1, j_uuid->valuestring);
            } else {
                qrzl_log("[认证开关] 二次认证已处于关闭状态");
            }
        } 
        else if (strcmp(new_switch_value, "1") == 0) {
            if (strcmp(current_switch, "1") != 0) {
                set_authentic_switch("1");
                qrzl_log("[认证开关] 已开启二次认证功能");
                wuxing_publish_command_result(client_p, "auth_switch", 1, j_uuid->valuestring);
            } else {
                qrzl_log("[认证开关] 二次认证已处于开启状态");
            }
        }
        else {
            qrzl_log("[认证开关] 错误的值: %s,只接受 '0'（关闭）或 '1'（开启）", 
                    new_switch_value);
        }
    }

    cJSON* macList = cJSON_GetObjectItem(j_value, "macList");
    if(macList != NULL && cJSON_IsString(macList) && macList->valuestring)
    {
        qrzl_log("macList:%s",macList->valuestring);
        if(strcmp(macList->valuestring, "1") == 0)
        {
            qrzl_log("MQTT服务端获取MACList指令");
            wuxing_publish_command_result(client_p, "macList", 1, j_uuid->valuestring);
        }
    }
    cJSON* j_host = cJSON_GetObjectItem(j_value, "host");
    cJSON* j_userName = cJSON_GetObjectItem(j_value, "userName");
    cJSON* j_mqPwd = cJSON_GetObjectItem(j_value, "mqPwd");
    if (j_host != NULL && cJSON_IsString(j_host) && j_userName != NULL && cJSON_IsString(j_userName) 
        && j_mqPwd != NULL && cJSON_IsString(j_mqPwd) && j_host->valuestring && j_userName->valuestring && j_mqPwd->valuestring)
    {
        snprintf(wuxing_new_mqtt_server, sizeof(wuxing_new_mqtt_server), "%s", j_host->valuestring);
        snprintf(wuxing_new_mqtt_username, sizeof(wuxing_new_mqtt_username), "%s", j_userName->valuestring);
        snprintf(wuxing_new_mqtt_password, sizeof(wuxing_new_mqtt_password), "%s", j_mqPwd->valuestring);
        qrzl_log("wuxing mqtt new server: %s, username: %s, password: %s", wuxing_new_mqtt_server, wuxing_new_mqtt_username, wuxing_new_mqtt_password);
        cfg_set("qrzl_cloud_mqtt_wuxing_new_server", wuxing_new_mqtt_server);
        cfg_set("qrzl_cloud_mqtt_wuxing_new_username", wuxing_new_mqtt_username);
        cfg_set("qrzl_cloud_mqtt_wuxing_new_password", wuxing_new_mqtt_password);
        // 创建断开线程
        pthread_t thread_receive;
        pthread_create(&thread_receive, NULL, wuxing_mqtt_update_server, (void *)client_p);
        pthread_detach(thread_receive);  // 让线程自动回收
    }
    return 0;
}
int wuxing_order_msg_handler(cJSON* j_value, MQTTClient* client_p)
{
    cJSON *j_uuid = cJSON_GetObjectItem(j_value, "uuid");
    cJSON *j_t = cJSON_GetObjectItem(j_value, "t");
    cJSON *j_o = cJSON_GetObjectItem(j_value, "o");//预留的
    if (j_uuid == NULL || !cJSON_IsString(j_uuid) || j_t == NULL || !cJSON_IsString(j_t))
    {
        qrzl_log("公共参数未正常接收,不做命令处理");
        return -1;
    }

    wuxing_cmd_handler(j_value, client_p);
    
    return 0;
}
int wuxing_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    cJSON *j_value = cJSON_Parse((char*)message->payload);
    if (j_value != NULL)
    {
        if (cJSON_IsObject(j_value))
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            wuxing_order_msg_handler(j_value, client_p);
        }
            cJSON_Delete(j_value);
    }else
    {
        qrzl_log("收到的不是JSON体");
        if(wuxing_new_server_flag == 1)
        {
            if(++new_server_fail_count >= 3)
            {
                pthread_t thread_NO_JSON;
                pthread_create(&thread_NO_JSON, NULL, wuxing_revert_to_factory, (void *)client_p);
                pthread_detach(thread_NO_JSON);  // 让线程自动回收
            }
        }
    }

    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}
void wuxing_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    wuxing_mqtt_connect(client_p);
    wuxing_publish_device_info(client_p);
}
void wuxing_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    // 这里才算真的被推送成功
    qrzl_log("Message with token %d delivered", dt);
}
static void *wuxing_mqtt_update_server(void *arg)
{
    //进到更改地址线程就认为在新地址了
    set_wuxing_new_server_flag(1);
    sleep(10);
    MQTTClient *client_p = (MQTTClient *)arg;  // 进行类型转换
    qrzl_log("in thread disconn MQTT ...");
    MQTTClient_disconnect(*client_p, 0);
    MQTTClient_destroy(client_p);
    // 重新设置MQTT的服务器信息
    MQTTClient_create(client_p, wuxing_new_mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);
    MQTTClient_setCallbacks(*client_p, client_p, wuxing_mqtt_connlost, wuxing_message_arrived, wuxing_on_message_delivered);
    // 连接MQTT服务器
    wuxing_mqtt_connect(client_p);
    


    wuxing_publish_device_info(client_p);
    return NULL;
}
static void wuxing_cloud_client_start()
{
    update_device_static_data();


    char flag_buf[2] = {0};
    //先看看有没有修改过的地址历史
    cfg_get_item("qrzl_cloud_mqtt_wuxing_new_server", wuxing_new_mqtt_server, sizeof(wuxing_new_mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_wuxing_new_username", wuxing_new_mqtt_username, sizeof(wuxing_new_mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_wuxing_new_password", wuxing_new_mqtt_password, sizeof(wuxing_new_mqtt_password));
    cfg_get_item("wuxing_new_server_flag", flag_buf, sizeof(flag_buf));
    wuxing_new_server_flag = atoi(flag_buf);

    if (wuxing_new_server_flag == 1 && wuxing_new_mqtt_server[0] != '\0')
    {

        strncpy(mqtt_server, wuxing_new_mqtt_server, sizeof(wuxing_new_mqtt_server));
        strncpy(mqtt_username, wuxing_new_mqtt_username, sizeof(wuxing_new_mqtt_username));
        strncpy(mqtt_password, wuxing_new_mqtt_password, sizeof(wuxing_new_mqtt_password));
    }
    else
    {
        get_factory_MQTT_info();
        wuxing_new_server_flag = 0;
    }
    MQTTClient client;
    
    MQTTClient_create(&client, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, wuxing_mqtt_connlost, wuxing_message_arrived, wuxing_on_message_delivered);

    wuxing_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, wuxing_publish_loop, &client) != 0) {
        qrzl_err("Failed to create wuxing_publish_loop thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);

    return;
}
/* ========================================= end mqtt wuxing 类型的处理 ==================================================================== */
#endif /* QRZL_MQTT_CLIENT_WUXING */
void* start_mqtt_control_client()
{
    qrzl_log("start_mqtt_control_client");
    cfg_get_item("qrzl_cloud_mqtt_server", mqtt_server, sizeof(mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_username", mqtt_username, sizeof(mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_password", mqtt_password, sizeof(mqtt_password));

    qrzl_log("server: %s, username: %s, password: %s", mqtt_server, mqtt_username, mqtt_password);

    // Use compile-time macro selection instead of runtime configuration
#ifdef QRZL_MQTT_CLIENT_TYPE_1
    qrzl_log("开始 mqtt mqtt_type_1格式云接口处理");
    t1_cloud_client_start();
#elif defined(QRZL_MQTT_CLIENT_KY)
    qrzl_log("开始 mqtt KY 格式云接口处理");
    ky_cloud_client_start();
#elif defined(QRZL_MQTT_CLIENT_YIMING)
    qrzl_log("开始 mqtt MQTT_YIMING 格式云接口处理");
    yiming_cloud_client_start();
#elif defined(QRZL_MQTT_CLIENT_WUXING)
    qrzl_log("开始 mqtt MQTT_WUXING 格式云接口处理");
    wuxing_cloud_client_start();
#else
    qrzl_log("没有定义MQTT客户端类型宏，start_mqtt_control_client线程退出");
#endif
    return NULL;
}