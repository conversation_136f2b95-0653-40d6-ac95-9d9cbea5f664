#include <stdio.h>      // 可选（printf、perror）
#include <fcntl.h>      // open、O_WRONLY、O_TRUNC
#include <unistd.h>     // write、close
#include <string.h>     // strlen
#include <errno.h>
#include <strings.h>  // 支持 strcasecmp


#include	"wsIntrn.h"

// #include "qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"
#include "cloud_control/cmp_auth_control.h"

#define PROC_AUTH_PATH "/proc/cjportal/auth"
#define MAX_MAC_COUNT 50
#define MAX_MAC_LEN   32    // 每个MAC最大长度 "xx:xx:xx:xx:xx:xx"
#define NV_BUF_LEN    5048  // 存储缓冲区长度



extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;


static sym_fd_t	formSymtab = -1;

void websHeader(webs_t wp)
{
	a_assert(websValid(wp));

	websWrite(wp, T("HTTP/1.0 200 OK\n"));

	websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);
#ifdef WEBINSPECT_FIX
	websWrite(wp, T("X-Frame-Options: SAMEORIGIN\n"));
#endif	
	websWrite(wp, T("Pragma: no-cache\n"));
	websWrite(wp, T("Cache-control: no-cache\n"));
	websWrite(wp, T("Content-Type: text/html\n"));
	websWrite(wp, T("\n"));
	websWrite(wp, T("<html>\n"));
}


int websFormDefine(char_t *name, void (*fn)(webs_t wp, char_t *path, 
	char_t *query))
{
	a_assert(name && *name);
	a_assert(fn);

	if (fn == NULL) {
		return -1;
	}

	symEnter(formSymtab, name, valueInteger((int) fn), (int) NULL);
	return 0;
}

void websFooter(webs_t wp)
{
	a_assert(websValid(wp));

	websWrite(wp, T("</html>\n"));
}

int websFormHandler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, 
	char_t *url, char_t *path, char_t *query)
{	
	char_t		form_buf[FNAMESIZE];
	char_t		*cp, *form_name;
	sym_t		*sp;
	int			(*fn)(void *sock, char_t *path, char_t *args);

	a_assert(websValid(wp));
	a_assert(url && *url);
	a_assert(path && *path == '/');

	websStats.formHits++;
#ifdef WEBS_SECURITY
	if (strstr(query,"_method")) {
		printf("websFH: qry=%s\n",query);
		websError(wp, 405, T(""));
		return 1;
	}
#endif

	gstrncpy(form_buf, path, TSZ(form_buf)-1);
	if ((form_name = gstrchr(&form_buf[1], '/')) == NULL) {
		websError(wp, 200, T("Missing form name"));
		return 1;
	}
	form_name++;
	if ((cp = gstrchr(form_name, '/')) != NULL) {
		*cp = '\0';
	}

	sp = symLookup(formSymtab, form_name);
	if (sp == NULL) {
#ifdef WEBINSPECT_FIX
		websDone(wp, 0);
#else
		websError(wp, 200, T("Form %s is not defined"), form_name);
#endif		
	} else {
		fn = (int (*)(void *, char_t *, char_t *)) sp->content.value.integer;
		a_assert(fn);
		if (fn) {
			(*fn)((void*) wp, form_name, query);
		}
	}
	return 1;
}


void websFormClose()
{
	if (formSymtab != -1) {
		symClose(formSymtab);
		formSymtab = -1;
	}
}


void websFormOpen()
{
	formSymtab = symOpen(WEBS_SYM_INIT);
}

// 写入/proc文件系统授权
static int write_mac_to_proc(const char *mac) {

#ifdef ZXIC_ONELINK_TEST

    // 获取黑名单mac
	char blocked_macs[1024] = {0};
	cfg_get_item("blocked_macs", blocked_macs, sizeof(blocked_macs));

	char search_pattern_2[40] = {0};
    snprintf(search_pattern_2, sizeof(search_pattern_2), "%s;", mac);

    char *pos2 = strstr(blocked_macs, search_pattern_2);
    if (pos2 != NULL) {
        printf("corem removing MAC %s from blocked_macs\n", mac);

        // 将找到的MAC从blocked_macs中删除
        size_t len_to_remove = strlen(search_pattern_2);
        memmove(pos2, pos2 + len_to_remove, strlen(pos2 + len_to_remove) + 1); // +1拷贝末尾'\0'

        // 更新NV
        cfg_set("blocked_macs", blocked_macs);
    } else {
        printf("corem MAC %s not found in blocked_macs, skip\n", mac);
    }

    // 清理该MAC地址相关的所有iptables规则
	char cmd_allow[256];
	snprintf(cmd_allow, sizeof(cmd_allow), "/sbin/one_link_authenticated.sh allow %s", mac);
	system(cmd_allow);
#elif !defined(QRZL_WIFIDOG_ONELINK)
    int fd = open(PROC_AUTH_PATH, O_WRONLY | O_TRUNC);
    if (fd < 0) {
        printf("Failed to open %s: %s\n", PROC_AUTH_PATH, strerror(errno));
        return -1;
    }

    char line[64];
    int len = snprintf(line, sizeof(line), "%s\n", mac);

    if (write(fd, line, len) < 0) {
        printf("Failed to write to %s: %s\n", PROC_AUTH_PATH, strerror(errno));
        close(fd);
        return -1;
    }

    close(fd);
#endif
    return 0;
}

/**
 * 在 mac_list 末尾添加一个 mac（如果不存在）
 * mac_list: 存储所有mac的字符串 (以 ; 分隔, 末尾也有 ;)
 * new_mac: 要添加的mac
 * 返回值: 0 表示已存在，1 表示添加成功，-1 表示缓冲区不足
 */
int nv_add_mac(char *mac_list, const char *new_mac, size_t buf_size) {
    char temp[64];
    size_t len;

    if (!mac_list || !new_mac) return -1;

    // 构造 "mac;" 格式，避免匹配到类似前缀
    snprintf(temp, sizeof(temp), "%s;", new_mac);

    // 如果已存在则直接返回
    if (strstr(mac_list, temp) != NULL) {
        return 0; // 已存在
    }

    len = strlen(mac_list);
    if (len + strlen(temp) >= buf_size) {
        return -1; // 缓冲区不足
    }

    // 追加到末尾
    strcat(mac_list, temp);
    return 1; // 添加成功
}

/*****************************************************************************************************
 * @brief 将任意格式的MAC地址（可能含有':'或'-'或不含）标准化为指定格式
 *
 * @param input         输入MAC地址字符串（如 "3C:67:FD:74:D2:FB", "3c-67-fd-74-d2-fb", "3c67fd74d2fb"）
 * @param output        输出结果缓冲区
 * @param out_size      输出缓冲区大小，至少应为 18
 * @param format_char   输出格式：':' 表示冒号分隔，'-' 表示短横线分隔，'\0' 表示无分隔
 *
 * @return int 0 表示成功，-1 表示失败（如非法输入）
 *****************************************************************************************************/
int convert_mac_format2(const char *input, char *output, size_t out_size, char format_char) {
    char temp[13] = {0}; // 临时存储12个16进制字符 + 结尾0
    int len = 0;

    if (!input || !output || out_size < 13 || (format_char && out_size < 18)) {
        return -1;
    }

    // 1. 提取16进制字符（忽略':' 或 '-'）
    int i;
    for ( i = 0; input[i] && len < 12; ++i) {
        if (isxdigit((unsigned char)input[i])) {
            temp[len++] = tolower((unsigned char)input[i]);
        }
    }

    if (len != 12) {
        return -1; // 不是合法MAC地址
    }

    // 2. 格式化输出
    if (format_char == '\0') {
        // 不带分隔符
        snprintf(output, out_size, "%s", temp);
    } else {
        // 带分隔符，格式 xx:xx:xx:xx:xx:xx
        // 17字节+1结尾0，确保缓冲区至少18
        snprintf(output, out_size,
            "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c",
            temp[0], temp[1], format_char,
            temp[2], temp[3], format_char,
            temp[4], temp[5], format_char,
            temp[6], temp[7], format_char,
            temp[8], temp[9], format_char,
            temp[10], temp[11]);
    }

    return 0;
}

int update_authed_mac_list2(const char *authed_nv_name, const char *new_mac_str)
{
    if (new_mac_str == NULL) {
        return -1;
    }

    char nv_buf[NV_BUF_LEN] = {0};
    char all_macs[MAX_MAC_COUNT * 2][MAX_MAC_LEN]; // 临时存放旧+新 MAC
    int mac_count = 0;
    int i;

    // 1. 读取旧的 nv
    cfg_get_item(authed_nv_name, nv_buf, sizeof(nv_buf));
    printf("[DEBUG] 原始NV: %s\n", nv_buf);

    /**
     *  strtok 用一个 全局静态变量 来保存解析位置，所以：
            不能嵌套使用。
            多线程不安全。
        strtok_r 把保存解析位置的变量交给调用者（saveptr），因此：
            可以嵌套使用。
            可以在多线程环境下独立处理。
     */

    // 2. 拆分旧的 MAC
    {
        char *token, *saveptr;
        token = strtok_r(nv_buf, ";", &saveptr);
        while (token && mac_count < MAX_MAC_COUNT * 2) {
            strncpy(all_macs[mac_count], token, MAX_MAC_LEN - 1);
            all_macs[mac_count][MAX_MAC_LEN - 1] = '\0';
            mac_count++;
            token = strtok_r(NULL, ";", &saveptr);
        }
    }
    printf("[DEBUG] 已有MAC数量: %d\n", mac_count);

    // 3. 拆分新传入的 MAC
    {
        char tmp[NV_BUF_LEN];
        strncpy(tmp, new_mac_str, sizeof(tmp) - 1);
        tmp[sizeof(tmp) - 1] = '\0';

        char *token, *saveptr;
        token = strtok_r(tmp, ";", &saveptr);
        while (token && mac_count < MAX_MAC_COUNT * 2) {
            int exists = 0;
            for (i = 0; i < mac_count; i++) {
                if (strcmp(all_macs[i], token) == 0) {
                    exists = 1;
                    break;
                }
            }
            if (!exists) {
                strncpy(all_macs[mac_count], token, MAX_MAC_LEN - 1);
                all_macs[mac_count][MAX_MAC_LEN - 1] = '\0';
                mac_count++;
                printf("[DEBUG] 新增MAC: %s\n", token);
            } else {
                printf("[DEBUG] 跳过已存在MAC: %s\n", token);
            }
            token = strtok_r(NULL, ";", &saveptr);
        }
    }

    // 4. 如果超过 MAX_MAC_COUNT，只保留后面的
    int start = 0;
    if (mac_count > MAX_MAC_COUNT) {
        start = mac_count - MAX_MAC_COUNT;
        printf("[DEBUG] MAC超过%d个，丢弃前面%d个\n", MAX_MAC_COUNT, start);
    }

    // 5. 拼接回字符串
    char new_nv[NV_BUF_LEN] = {0};
    for (i = start; i < mac_count; i++) {
        strcat(new_nv, all_macs[i]);
        if (i < mac_count - 1) strcat(new_nv, ";");
    }

    // 6. 存回 nv
    printf("[DEBUG] 更新后的NV: %s\n", new_nv);
    cfg_set(authed_nv_name, new_nv);

    return 0;
}

/**************************************************************
 * 电信认证回调接口
 *************************************************************/
void CMPauthCallbackHandler(webs_t wp)
{
    char terminalMac_out[32] = {0};
    const char *token = websGetVar(wp, T("token"), T("NULL"));
    const char *mac = websGetVar(wp, T("mac"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH token: %s\n", token);
    printf("CALLBACK -> AUTH mac (device): %s\n", mac);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(token, "NULL") != 0 && strcmp(terminalMac, "NULL") != 0) {
        printf("CALLBACK -> 认证成功, 正在加入授权文件\n");

        if (convert_mac_format2(terminalMac, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                // 保存 NV
                // char nv_old[NV_BUF_LEN] = {0};
                // cfg_get_item("cmp_authed_mac", nv_old, sizeof(nv_old));
                char mac_nv[32] = {0};
                convert_mac_format2(terminalMac, mac_nv, sizeof(mac_nv), ':');
				// int save_res = nv_add_mac(nv_old, mac_nv, sizeof(nv_old));
				int save_res = update_authed_mac_list2("cmp_authed_mac", mac_nv);
				if (save_res == 0) {
					// nv_set_item(NV_RO, "cmp_authed_mac", nv_old, 1);
					// cfg_set("cmp_authed_mac", nv_old);
					// nv_commit(NV_RO);
					// printf("CALLBACK -> cmp_authed_mac 更新为: %s\n", nv_old);
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> cmp_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }
}

/**************************************************************
 * 移动认证回调接口
 *************************************************************/
void One_Link_authCallbackHandler(webs_t wp)
{
    char terminalMac_out[32] = {0};
    const char *token = websGetVar(wp, T("token"), T("NULL"));
    const char *mac = websGetVar(wp, T("mac"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH token: %s\n", token);
    printf("CALLBACK -> AUTH mac (device): %s\n", mac);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(token, "NULL") != 0 && strcmp(terminalMac, "NULL") != 0) {
        printf("CALLBACK -> 认证成功, 正在加入授权文件\n");

        if (convert_mac_format2(terminalMac, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                // 保存 NV
                // char nv_old[NV_BUF_LEN] = {0};
                // cfg_get_item("one_link_authed_mac", nv_old, sizeof(nv_old));
                char mac_nv[32] = {0};
                convert_mac_format2(terminalMac, mac_nv, sizeof(mac_nv), ':');
				// int save_res = nv_add_mac(nv_old, mac_nv, sizeof(nv_old));
				int save_res = update_authed_mac_list2("one_link_authed_mac", mac_nv);
				if (save_res == 0) {
					// nv_set_item(NV_RO, "one_link_authed_mac", nv_old, 1);
					// cfg_set("one_link_authed_mac", nv_old);
					// nv_commit(NV_RO);
					// printf("CALLBACK -> one_link_authed_mac 更新为: %s\n", nv_old);
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> one_link_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }
}


void MY_authCallbackHandler(webs_t wp)
{
    char terminalMac_out[32] = {0};
    const char *token = websGetVar(wp, T("codeToken"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH token: %s\n", token);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(token, "NULL") != 0 && strcmp(terminalMac, "NULL") != 0) {
        printf("CALLBACK -> 认证成功, 正在加入授权文件\n");

        if (convert_mac_format2(terminalMac, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                char mac_nv[32] = {0};
                convert_mac_format2(terminalMac, mac_nv, sizeof(mac_nv), ':');

                // 获取运营商
                char save_authed_nv_name[25] = {0};
                char imsi_str[32] = {0};
                int isp = 0;
                cfg_get_item("sim_imsi", imsi_str, sizeof(imsi_str));
                isp = get_isp_by_imsi(imsi_str);

                printf("imsi_str: %s", imsi_str);
                printf("isp: %d", isp);

                switch (isp)
                {
                    case 1:
                        // 移动
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "one_link_authed_mac");
                        break;
                    case 2:
                        // 联通
                        break;
                    case 3:
                        // 电信
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "cmp_authed_mac");
                        break;
                    default:
                        break;
                }

				int save_res = update_authed_mac_list2(save_authed_nv_name, mac_nv);
				if (save_res == 0) {
					printf("CALLBACK -> MAC地址已保存到NV: %s\n", save_authed_nv_name);

#ifdef QRZL_WIFIDOG_ONELINK
					// 通知WiFiDog客户端已认证成功
					// 创建一个临时token文件，供wifidog_auth_auth_handler使用
					char token_file[256] = {0};
					snprintf(token_file, sizeof(token_file), "/tmp/wifidog_token_%s", terminalMac_out);

					FILE *fp = fopen(token_file, "w");
					if (fp) {
						fprintf(fp, "%s", token);
						fclose(fp);
						printf("CALLBACK -> 已创建token文件: %s\n", token_file);
					}

					// 设置认证成功标志
					char auth_flag_file[256] = {0};
					snprintf(auth_flag_file, sizeof(auth_flag_file), "/tmp/wifidog_auth_%s", terminalMac_out);
					fp = fopen(auth_flag_file, "w");
					if (fp) {
						fprintf(fp, "authenticated");
						fclose(fp);
						printf("CALLBACK -> 已创建认证标志文件: %s\n", auth_flag_file);
					}

					// 直接重定向到WiFiDog auth接口，不显示成功页面
					// 参考http_callback_auth函数，需要token参数而不是gw_id
					// 动态获取WiFiDog参数（从login接口中获取）
					char auth_url[512] = {0};
					char gw_address[64] = {0};
					char gw_port[16] = {0};

					// 从配置或session中获取WiFiDog参数
					cfg_get_item("wifidog_gw_address", gw_address, sizeof(gw_address));
					cfg_get_item("wifidog_gw_port", gw_port, sizeof(gw_port));

					// 设置默认值
					if (strlen(gw_address) == 0) {
						strcpy(gw_address, "*************");
					}
					if (strlen(gw_port) == 0) {
						strcpy(gw_port, "2060");
					}

					// 构建正确的WiFiDog认证URL，使用token参数
					snprintf(auth_url, sizeof(auth_url),
						"http://%s:%s/wifidog/auth?token=%s", gw_address, gw_port, token);

					printf("CALLBACK -> 直接重定向到WiFiDog认证接口: %s\n", auth_url);

					// 使用原生HTTP重定向而不是websRedirect，确保能访问外部URL
					// 如果HTTP重定向失败，页面会显示JavaScript备用重定向
					char *redirect_html = NULL;
					fmtAlloc(&redirect_html, 2048,
						"<!DOCTYPE html>\n"
						"<html>\n"
						"<head>\n"
						"  <meta charset=\"UTF-8\">\n"
						"  <title>正在跳转...</title>\n"
						"  <script>\n"
						"    // 立即跳转到WiFiDog认证接口\n"
						"    window.location.href = '%s';\n"
						"  </script>\n"
						"</head>\n"
						"<body>\n"
						"  <p>正在跳转到认证页面...</p>\n"
						"  <p>如果没有自动跳转，请<a href='%s'>点击这里</a></p>\n"
						"</body>\n"
						"</html>",
						auth_url, auth_url);

					websWrite(wp, T("HTTP/1.1 302 Found\r\n"));
					websWrite(wp, T("Location: %s\r\n"), auth_url);
					websWrite(wp, T("Content-Type: text/html; charset=UTF-8\r\n"));
					websWrite(wp, T("Cache-Control: no-cache\r\n"));
					websWrite(wp, T("Connection: close\r\n\r\n"));
					websWrite(wp, redirect_html);
					websDone(wp, 302);

					if (redirect_html) {
						bfree(B_L, redirect_html);
					}
#else
					websRedirect(wp, "/auth_success.html");  // 成功跳转
#endif
				} else {
					printf("CALLBACK -> one_link_authed_mac 长度过长，或已存在. code: %d\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
#ifdef QRZL_WIFIDOG_ONELINK
            // 显示认证失败页面，使用与auth_failed.html相同的样式
            char *failed_html = NULL;
            fmtAlloc(&failed_html, 4096,
                "<!DOCTYPE html>\n"
                "<html lang=\"zh-CN\">\n"
                "<head>\n"
                "  <meta charset=\"UTF-8\" />\n"
                "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n"
                "  <title>认证失败</title>\n"
                "  <style>\n"
                "    body {\n"
                "      margin: 0;\n"
                "      padding: 0;\n"
                "      font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n"
                "      background: linear-gradient(to right, #ffe0e0, #ffffff);\n"
                "      display: flex;\n"
                "      justify-content: center;\n"
                "      align-items: center;\n"
                "      min-height: 100vh;\n"
                "    }\n"
                "    .box {\n"
                "      text-align: center;\n"
                "      background-color: #ffffff;\n"
                "      padding: 2em 1.5em;\n"
                "      border-radius: 1.2em;\n"
                "      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n"
                "      animation: fadeIn 1s ease-in-out;\n"
                "      width: 90%%;\n"
                "      max-width: 400px;\n"
                "      box-sizing: border-box;\n"
                "    }\n"
                "    .box h1 {\n"
                "      font-size: 1.8em;\n"
                "      color: #f44336;\n"
                "      margin-bottom: 0.5em;\n"
                "    }\n"
                "    .icon {\n"
                "      margin-bottom: 1em;\n"
                "      width: 56px;\n"
                "      height: 56px;\n"
                "      margin-left: auto;\n"
                "      margin-right: auto;\n"
                "    }\n"
                "    .back-button {\n"
                "      margin-top: 2em;\n"
                "      padding: 0.7em 1.5em;\n"
                "      font-size: 1em;\n"
                "      background-color: #f44336;\n"
                "      color: white;\n"
                "      border: none;\n"
                "      border-radius: 0.5em;\n"
                "      cursor: pointer;\n"
                "      transition: background-color 0.3s ease;\n"
                "    }\n"
                "    .back-button:hover {\n"
                "      background-color: #d32f2f;\n"
                "    }\n"
                "    @keyframes fadeIn {\n"
                "      from {\n"
                "        opacity: 0;\n"
                "        transform: translateY(-10px);\n"
                "      }\n"
                "      to {\n"
                "        opacity: 1;\n"
                "        transform: translateY(0);\n"
                "      }\n"
                "    }\n"
                "  </style>\n"
                "</head>\n"
                "<body>\n"
                "  <div class=\"box\">\n"
                "    <div class=\"icon\" aria-label=\"失败图标\" role=\"img\">\n"
                "      <svg viewBox=\"0 0 64 64\" width=\"56\" height=\"56\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" stroke=\"#f44336\" stroke-width=\"6\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n"
                "        <circle cx=\"32\" cy=\"32\" r=\"30\" fill=\"#fdecea\" />\n"
                "        <line x1=\"20\" y1=\"20\" x2=\"44\" y2=\"44\" />\n"
                "        <line x1=\"44\" y1=\"20\" x2=\"20\" y2=\"44\" />\n"
                "      </svg>\n"
                "    </div>\n"
                "    <h1>认证失败</h1>\n"
                "    <button class=\"back-button\" onclick=\"history.back()\">返回认证</button>\n"
                "  </div>\n"
                "</body>\n"
                "</html>");

            websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
            websWrite(wp, T("Content-Type: text/html; charset=UTF-8\r\n"));
            websWrite(wp, T("Cache-Control: no-cache\r\n"));
            websWrite(wp, T("Connection: close\r\n\r\n"));
            websWrite(wp, failed_html);
            websDone(wp, 200);

            if (failed_html) {
                bfree(B_L, failed_html);
            }
#else
            websRedirect(wp, "/auth_failed.html");
#endif
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
#ifdef QRZL_WIFIDOG_ONELINK
        // 显示认证失败页面，使用与auth_failed.html相同的样式
        char *failed_html = NULL;
        fmtAlloc(&failed_html, 4096,
            "<!DOCTYPE html>\n"
            "<html lang=\"zh-CN\">\n"
            "<head>\n"
            "  <meta charset=\"UTF-8\" />\n"
            "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n"
            "  <title>认证失败</title>\n"
            "  <style>\n"
            "    body {\n"
            "      margin: 0;\n"
            "      padding: 0;\n"
            "      font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n"
            "      background: linear-gradient(to right, #ffe0e0, #ffffff);\n"
            "      display: flex;\n"
            "      justify-content: center;\n"
            "      align-items: center;\n"
            "      min-height: 100vh;\n"
            "    }\n"
            "    .box {\n"
            "      text-align: center;\n"
            "      background-color: #ffffff;\n"
            "      padding: 2em 1.5em;\n"
            "      border-radius: 1.2em;\n"
            "      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n"
            "      animation: fadeIn 1s ease-in-out;\n"
            "      width: 90%%;\n"
            "      max-width: 400px;\n"
            "      box-sizing: border-box;\n"
            "    }\n"
            "    .box h1 {\n"
            "      font-size: 1.8em;\n"
            "      color: #f44336;\n"
            "      margin-bottom: 0.5em;\n"
            "    }\n"
            "    .icon {\n"
            "      margin-bottom: 1em;\n"
            "      width: 56px;\n"
            "      height: 56px;\n"
            "      margin-left: auto;\n"
            "      margin-right: auto;\n"
            "    }\n"
            "    .back-button {\n"
            "      margin-top: 2em;\n"
            "      padding: 0.7em 1.5em;\n"
            "      font-size: 1em;\n"
            "      background-color: #f44336;\n"
            "      color: white;\n"
            "      border: none;\n"
            "      border-radius: 0.5em;\n"
            "      cursor: pointer;\n"
            "      transition: background-color 0.3s ease;\n"
            "    }\n"
            "    .back-button:hover {\n"
            "      background-color: #d32f2f;\n"
            "    }\n"
            "    @keyframes fadeIn {\n"
            "      from {\n"
            "        opacity: 0;\n"
            "        transform: translateY(-10px);\n"
            "      }\n"
            "      to {\n"
            "        opacity: 1;\n"
            "        transform: translateY(0);\n"
            "      }\n"
            "    }\n"
            "  </style>\n"
            "</head>\n"
            "<body>\n"
            "  <div class=\"box\">\n"
            "    <div class=\"icon\" aria-label=\"失败图标\" role=\"img\">\n"
            "      <svg viewBox=\"0 0 64 64\" width=\"56\" height=\"56\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" stroke=\"#f44336\" stroke-width=\"6\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n"
            "        <circle cx=\"32\" cy=\"32\" r=\"30\" fill=\"#fdecea\" />\n"
            "        <line x1=\"20\" y1=\"20\" x2=\"44\" y2=\"44\" />\n"
            "        <line x1=\"44\" y1=\"20\" x2=\"20\" y2=\"44\" />\n"
            "      </svg>\n"
            "    </div>\n"
            "    <h1>认证失败</h1>\n"
            "    <button class=\"back-button\" onclick=\"history.back()\">返回认证</button>\n"
            "  </div>\n"
            "</body>\n"
            "</html>");

        websWrite(wp, T("HTTP/1.1 200 OK\r\n"));
        websWrite(wp, T("Content-Type: text/html; charset=UTF-8\r\n"));
        websWrite(wp, T("Cache-Control: no-cache\r\n"));
        websWrite(wp, T("Connection: close\r\n\r\n"));
        websWrite(wp, failed_html);
        websDone(wp, 200);

        if (failed_html) {
            bfree(B_L, failed_html);
        }
#else
        websRedirect(wp, "/auth_failed.html");
#endif
    }

}

void BW_authCallbackHandler(webs_t wp)
{
    // status=success&iccid=89860405192580177831&mac=3c%3A68%3A01%3Acb%3A44%3Ac4&terminalMac=3c%3A68%3A01%3Ada%3A87%3A04
    
    char terminalMac_out[32] = {0};

    const char *status = websGetVar(wp, T("status"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH status: %s\n", status);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(status, "success") == 0 && strcmp(terminalMac, "NULL") != 0) {
        // 将terminalMac url解码
        char terminalMac_decoded[32] = {0};
        url_decode(terminalMac, terminalMac_decoded, sizeof(terminalMac_decoded));
        
        if (convert_mac_format2(terminalMac_decoded, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                char mac_nv[32] = {0};
                convert_mac_format2(terminalMac_decoded, mac_nv, sizeof(mac_nv), ':');

                // 获取运营商
                char save_authed_nv_name[25] = {0};
                char imsi_str[32] = {0};
                int isp = 0;
                cfg_get_item("sim_imsi", imsi_str, sizeof(imsi_str));
                isp = get_isp_by_imsi(imsi_str);

                printf("imsi_str: %s", imsi_str);
                printf("isp: %d", isp);

                switch (isp)
                {
                    case 1:
                        // 移动
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "one_link_authed_mac");
                        break;
                    case 2:
                        // 联通
                        break;
                    case 3:
                        // 电信
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "cmp_authed_mac");
                        break;
                    default:
                        break;
                }

				int save_res = update_authed_mac_list2(save_authed_nv_name, mac_nv);
				if (save_res == 0) {
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> one_link_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }
}

#ifdef QRZL_AUTH_CHUANGSAN
void CS_authCallbackHandler(webs_t wp)
{
    MY_authCallbackHandler(wp);
}
#endif