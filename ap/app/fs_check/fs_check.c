#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include <string.h>
#include <sys/stat.h>
#include "fs_check.h"

extern int mount_fs_partition(struct mtd_fs *p_fs);
extern int mtd_erase_partition(const char* partition_name);
extern int mtd_write_partition(const char* partition_name, const char* image_file);
extern int unmount_fs_partition(struct mtd_fs *p_fs);
extern int check_userdata_is_normal();
extern char *g_path_prefix;

static struct mtd_fs fs_array[] = {
#ifdef USE_OEM_FS_QRZL_OTA
	{
		.patition_name="oem",
		.mtd_index=-1,
		.mount_point="/mnt/oem",
		.image_file="/etc_rw/ap_oem.img",
		.fs_type="jffs2",
		.mount_opt="",
		.sh_cmd=NULL
	},
#endif
#ifdef USE_UBIFS
#ifdef CONFIG_SYSTEM_CAP
	{
		.patition_name="capuserdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_capuserdata.img",
		.fs_type="ubifs",
		.mount_opt="",
		.sh_cmd=NULL
	},
#else
	{
		.patition_name="userdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_userdata.img",
		.fs_type="ubifs",
		.mount_opt="",
		.sh_cmd=NULL
	},
#endif
#else
#ifdef CONFIG_SYSTEM_CAP
	{
		.patition_name="capuserdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_capuserdata.img",
		.fs_type="jffs2",
		.mount_opt="",
		.sh_cmd=NULL
	},
#else
	{
		.patition_name="userdata",
		.mtd_index=-1,
		.mount_point="/mnt/userdata",
		.image_file="/etc_ro/ap_userdata.img",
		.fs_type="jffs2",
		.mount_opt="",
		.sh_cmd=NULL
	},
#endif
#endif
#ifndef CONFIG_SYSTEM_CAP
	{
		.patition_name="nvrofs",
		.mtd_index=-1,
		.mount_point="/mnt/nvrofs",
		.image_file=NULL,
		.fs_type="jffs2",
		.mount_opt="-o ro",
		.sh_cmd=NULL
	}
#endif
};

int main(int argc, char *argv[]) {

	int i;
	int ret;
	int result;
	int fs_cnt = sizeof(fs_array)/sizeof(fs_array[0]);

	//recovery版本文件系统挂载
	if (argc > 1 && 0 == strcmp(argv[1], "recovery"))
	{
		g_path_prefix = "/recovery";
		for(i = 0; i < fs_cnt; i++) {
			printf("fs_check mount_fs_partition begin\n");
			ret = mount_fs_partition(&fs_array[i]);
			printf("fs_check mount_fs_partition mount result %d\n",ret);
		}
		return 0;
	}

#ifdef USE_OEM_FS_QRZL_OTA
	//upgrade oem 分区升级
	if (argc > 2 && 0 == strcmp(argv[1], "upgrade") && 0 == strcmp(argv[2], "oem"))
	{
		printf("fs_check upgrade oem partition begin\n");

		// 查找oem分区配置
		struct mtd_fs *oem_fs = NULL;
		for(i = 0; i < fs_cnt; i++) {
			if (0 == strcmp(fs_array[i].patition_name, "oem")) {
				oem_fs = &fs_array[i];
				break;
			}
		}

		if (oem_fs == NULL) {
			printf("fs_check oem partition not found in configuration\n");
			return -1;
		}

		// 检查image文件是否存在
		if (oem_fs->image_file == NULL) {
			printf("fs_check oem image file not configured\n");
			return -1;
		}

		struct stat statbuff = {0};
		if(stat(oem_fs->image_file, &statbuff) < 0) {
			printf("fs_check oem image file %s does not exist\n", oem_fs->image_file);
			return -1;
		}

		printf("fs_check oem image file %s found, size: %ld bytes\n", oem_fs->image_file, statbuff.st_size);

		// 卸载oem分区
		printf("fs_check unmounting oem partition\n");
		ret = unmount_fs_partition(oem_fs);
		if (ret) {
			printf("fs_check unmount oem partition failed, ret = %d\n", ret);
		}

		// 擦除oem分区
		printf("fs_check erasing oem partition\n");
		ret = mtd_erase_partition(oem_fs->patition_name);
		if (ret) {
			printf("fs_check erase oem partition failed, ret = %d\n", ret);
			return -1;
		}

		// 写入新的oem镜像
		printf("fs_check writing oem image to partition\n");
		ret = mtd_write_partition(oem_fs->patition_name, oem_fs->image_file);
		if (ret) {
			printf("fs_check write oem partition failed, ret = %d\n", ret);
			return -1;
		}

		// 重新挂载oem分区
		printf("fs_check mounting oem partition\n");
		ret = mount_fs_partition(oem_fs);
		if (ret) {
			printf("fs_check mount oem partition failed, ret = %d\n", ret);
			return -1;
		}

		printf("fs_check upgrade oem partition success!\n");
		return 0;
	}
#endif

	//normal版本文件系统挂载
	for(i = 0; i < fs_cnt; i++) {
		printf("fs_check mount_fs_partition begin\n"); 
		ret = mount_fs_partition(&fs_array[i]);
		if(ret)
		{
			unmount_fs_partition(&fs_array[i]);
			mtd_erase_partition(fs_array[i].patition_name);
			printf("fs_check mtd_erase %s\n", fs_array[i].patition_name);
			ret = mtd_write_partition(fs_array[i].patition_name, fs_array[i].image_file);
			printf("fs_check mtd_write %s ret = %d\n", fs_array[i].patition_name, ret);
			ret = mount_fs_partition(&fs_array[i]);
			if(ret)
				assert(0);
		}
#ifndef CONFIG_SYSTEM_CAP
		else
		{
			//挂载成功后检查文件是否被破坏，如果破坏则恢复
			if ((0 == strcmp(fs_array[i].patition_name, "userdata")))
			{
				result = check_userdata_is_normal();
			}
			else
			{
				result = 0;
			}
			if(result)
			{
				unmount_fs_partition(&fs_array[i]);
				//printf("fs_check mount %s fail\n", fs_array[i].patition_name);
				mtd_erase_partition(fs_array[i].patition_name);
				printf("fs_check mtd_erase %s\n", fs_array[i].patition_name);
				ret = mtd_write_partition(fs_array[i].patition_name, fs_array[i].image_file);
				printf("fs_check mtd_write %s ret = %d\n", fs_array[i].patition_name, ret);
				ret = mount_fs_partition(&fs_array[i]);
				if(ret)
					assert(0);
			}
		}
#endif
		printf("fs_check mount %s success!\n", fs_array[i].patition_name);
	}

	return 0;
}


