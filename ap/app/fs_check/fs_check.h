/*******************************************************************************
* ��Ȩ���� (C)2016, ����ͨѶ�ɷ����޹�˾��
* 
* �ļ�����:     fs_check.h
* �ļ���ʶ:     fs_check.h
* ����ժҪ:     nv��̨Ӧ��ͷ�ļ�
* ʹ�÷���:     #include <fs_check.h>
* 
* �޸�����        �汾��      �޸ı��        �޸���          �޸�����
* ------------------------------------------------------------------------------
* 2016/09/20       V1.0        Create          ����          ����
* 
*******************************************************************************/
#ifndef _FS_CHECK_H
#define _FS_CHECK_H

/*******************************************************************************
*                                   ͷ�ļ�                                     *
*******************************************************************************/

/*******************************************************************************
*                                   �궨��                                     *
*******************************************************************************/    

/*******************************************************************************
*                                �������Ͷ���                                  *
*******************************************************************************/
struct mtd_fs {
	char *patition_name;
	int  mtd_index;
	char *mount_point;
	char *image_file;
	char *fs_type;
	char *mount_opt;
	char *sh_cmd;	/*erase and mount succ,then run this shell command*/
};

/*******************************************************************************
*                                ȫ�ֱ�������                                  *
*******************************************************************************/

/*******************************************************************************
*                                ȫ�ֺ�������                                  *
*******************************************************************************/

#endif

