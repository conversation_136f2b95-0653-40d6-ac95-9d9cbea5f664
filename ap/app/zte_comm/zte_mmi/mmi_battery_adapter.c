/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_batterry_adapter.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��MMI��ȡ���͵����Ϣ����
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
******************************************************************************/


/************************************************************************************
                           ͷ�ļ�
***********************************************************************************/
#include <linux/netlink.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include "mmi_common.h"
#include "mmi_lcd.h"
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
#include <math.h> // For round() function
#ifdef QRZL_AW3215_CAPACITY_SUPPORT
#include <stdio.h>
#include <stdlib.h>
#endif
#endif

/*
extern SINT32 g_mmi_hightempvol;
extern SINT32 g_mmi_superhightempvol;
extern SINT32 g_mmi_lowtempvol;
extern SINT32 g_mmi_superlowtempvol;
*/
extern SINT32 g_mmi_batvoltageline[MMI_BAT_VOLTAGE_LEN];
#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0) || defined(JCV_HW_GS28V_V1))
extern SINT32 g_mmi_chgvoltageline[MMI_BAT_VOLTAGE_LEN];
#endif


//��ѹ-�������ñ���mmi�����������ĵ�ѹ���������ĵ�ѹ���бȶԣ��Ӷ��ҵ�������Χ
static int bat_volage[] = {
	3090, 3300, 3450, 3490, 3510, 3540, 3550, 3570, 3580, 3600, 3620,
	3650, 3670, 3710, 3740, 3780, 3850, 3900, 3950, 4000, 4060,

};


static T_zMmiChgStateStringItem g_chgStateStringTab[] = {
	{CHARGE_STATUS_FULL,     	STATE_FULL},
	{CHARGE_STATUS_CHARGING, 	STATE_CHARGING},
	{CHARGE_STATUS_DISCHARGING,	STATE_DISCHARGE},
	{CHARGE_STATUS_NOTCHARGING,	STATE_CHARGERROR}
};


/**********************************************************************************
*����˵���� ��ȡ��ѹֵ
***********************************************************************************/
SINT32 mmi_voltage_state_read(VOID)
{
	char buf_volt[CHARGE_VOLTAGE_LENGTH] = {0};
	FILE* fd_voltage = NULL;
	int len = 0;
	int voltagepower = 0;

	fd_voltage = fopen(CHARGE_VOLTAGE_PATH, "r");
	if (fd_voltage == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI open voltage file fail!\n");
		//MMI_ASSERT(0);
		return -1;
	}
	len = fread(buf_volt, 1, CHARGE_VOLTAGE_LENGTH, fd_voltage);
	if (len > 0) { //kw 3
		voltagepower = atoi(buf_volt);
		fclose(fd_voltage);
		slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI voltagepower=%d\n",voltagepower);
		return voltagepower;
	} else {
		perror("read voltage file failed");
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI read voltage file fail len = %d !\n", len);
		fclose(fd_voltage);
		return -1;
	}
}


/*****************************************************************************************
*����˵���� ���ݵ�ذٷֱ����õ��״̬ 10%/25%/normal
*******************************************************************************************/
#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
E_zMmi_Voltage_level mmi_get_battery_volt_percent_level(UINT32 bat_level, BOOL charging_status)
{
        E_zMmi_Voltage_level volt_lev = VOLT_MAX;
        slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI %s bat_level = %d\n", __func__, bat_level);

        if(charging_status) {
                //charging bat level
                if (bat_level < 20) {
                        volt_lev = VOLT_20PERCENTLEVEL;
                } else if (bat_level < 50) {
                        volt_lev = VOLT_50PERCENTLEVEL;
                } else if (bat_level < 75) {
                        volt_lev = VOLT_75PERCENTLEVEL;
				} else if (bat_level < 100) {
                        volt_lev = VOLT_NORMALLEVEL;
                } else {
                        volt_lev = VOLT_FULLLEVEL;
                }
        } else {
                //discharge bat level
                if (bat_level <= 5) {
					volt_lev = VOLT_5PERCENTLEVEL;
				} else if (bat_level <= 10) { //kw 3
					volt_lev = VOLT_10PERCENTLEVEL;
				} else if (bat_level <= 20) {
					volt_lev = VOLT_20PERCENTLEVEL;
				} else if (bat_level <= 25) {
					volt_lev = VOLT_25PERCENTLEVEL;
				} else if (bat_level <= 50) {
					volt_lev = VOLT_50PERCENTLEVEL;
				} else if (bat_level <= 75) {
					volt_lev = VOLT_75PERCENTLEVEL;
				} else if (bat_level < 100) {
					volt_lev = VOLT_NORMALLEVEL;
				} else {
					volt_lev = VOLT_FULLLEVEL;
				}
        }

        return volt_lev;
}
#endif

E_zMmi_Voltage_level mmi_set_battery_state(UINT32 bat_level)
{
	E_zMmi_Voltage_level volt_lev = VOLT_MAX;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_battery_state bat_level = %d\n", bat_level);
	if (bat_level <= 5) {
		volt_lev = VOLT_5PERCENTLEVEL;
	} else if (bat_level <= 10) { //kw 3
		volt_lev = VOLT_10PERCENTLEVEL;
	} else if (bat_level <= 20) {
		volt_lev = VOLT_20PERCENTLEVEL;
	} else if (bat_level <= 25) {
		volt_lev = VOLT_25PERCENTLEVEL;
	} else {
		volt_lev = VOLT_NORMALLEVEL;
	}
	return volt_lev;
}


/*****************************************************************************************
*����˵���� ����WEBUI��ظ���
*******************************************************************************************/
VOID mmi_set_webUI_batterypers(UINT32 bat_level)
{
	if (bat_level <= 5) {
		cfg_set(WEBUIBATTERYLEVEL, WEBUINOLEVEL);
	} else if (bat_level <= 25) { //kw 3
		cfg_set(WEBUIBATTERYLEVEL, WEBUIONELEVEL);
	} else if (bat_level <= 50) {
		cfg_set(WEBUIBATTERYLEVEL, WEBUITWOLEVEL);
	} else if (bat_level < 100) {
		cfg_set(WEBUIBATTERYLEVEL, WEBUITHRLEVEL);
	} else {
		cfg_set(WEBUIBATTERYLEVEL, WEBUIFOURLEVEL);
	}
}

/*****************************************************************************************
*����˵���� ����WEBUI���״̬
*******************************************************************************************/
VOID mmi_set_webUI_batterycharge(char* chg_sta, BOOL isFull)
{
	cfg_set(WEBUICHARGESTATTUS, chg_sta);
	if (isFull) {
		cfg_set(BATTERYPERS_NV, WEBUIFULLLEVEL);
		cfg_set(WEBUIBATTERYLEVEL, WEBUIFOURLEVEL);
	}
}
/*****************************************************************************************
*����˵���� ���ݵ�ѹ��ȡ��ص����ٷֱ�
*******************************************************************************************/
SINT32 get_voltage_level_from_table(SINT32 voltagepower)
{
	UINT32 bat_lev = 0;
	SINT32 i;

	if (voltagepower < g_mmi_batvoltageline[0]) {
		bat_lev = 0;
	} else if (voltagepower >= g_mmi_batvoltageline[MMI_BAT_VOLTAGE_LEN - 1]) {
		bat_lev = 100;
	} else {
		for (i = 0; i < MMI_BAT_VOLTAGE_LEN - 1; i++) {
			if (voltagepower >= g_mmi_batvoltageline[i] && voltagepower < g_mmi_batvoltageline[i + 1]) {
				bat_lev = i * 5;
				break;
			}
		}
	}
	return bat_lev;
}

#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
//#define LED_DIGITS_DISPLAY
E_zMmi_Charge_State mmi_get_charge_status(VOID);

#define MAX_PERCENT_CHANGE_PER_CALL 1

// Add MIN/MAX macros if not already defined globally
#ifndef MAX
#define MAX(a,b) (((a) > (b)) ? (a) : (b))
#endif
#ifndef MIN
#define MIN(a,b) (((a) < (b)) ? (a) : (b))
#endif

SINT32 get_charging_voltage_level_from_table(SINT32 voltagepower, BOOL charging_status)
{
#ifdef QRZL_AW3215_CAPACITY_SUPPORT
    /* Read capacity directly from aw3215 driver */
    FILE *fp;
    char capacity_str[16] = {0};
    int capacity = 0;

    fp = fopen("/sys/class/power_supply/battery/capacity", "r");
    if (fp != NULL) {
        if (fgets(capacity_str, sizeof(capacity_str), fp) != NULL) {
            capacity = atoi(capacity_str);
            if (capacity >= 0 && capacity <= 100) {
                fclose(fp);
                slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Read capacity from driver: %d%%\n", capacity);
                cfg_set("battery_capacity", capacity_str);
                return capacity;
            }
        }
        fclose(fp);
    }

    /* Fallback to voltage-based calculation if driver read fails */
    slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Failed to read capacity from driver, using voltage fallback\n");

    /* Simple voltage-based fallback calculation */
    if (voltagepower <= 3348) return 0;
    else if (voltagepower <= 3456) return 5;
    else if (voltagepower <= 3576) return 10;
    else if (voltagepower <= 3659) return 15;
    else if (voltagepower <= 3736) return 20;
    else if (voltagepower <= 3785) return 25;
    else if (voltagepower <= 3811) return 30;
    else if (voltagepower <= 3832) return 35;
    else if (voltagepower <= 3847) return 40;
    else if (voltagepower <= 3856) return 45;
    else if (voltagepower <= 3860) return 50;
    else if (voltagepower <= 3893) return 55;
    else if (voltagepower <= 3951) return 60;
    else if (voltagepower <= 3998) return 65;
    else if (voltagepower <= 4013) return 70;
    else if (voltagepower <= 4021) return 75;
    else if (voltagepower <= 4026) return 80;
    else if (voltagepower <= 4031) return 85;
    else if (voltagepower <= 4045) return 90;
    else if (voltagepower <= 4084) return 95;
    else return 100;
#else
    UINT32 raw_bat_lev = 0;
    UINT32 tentative_final_bat_lev = 0; // Before directional constraint
    UINT32 final_bat_lev = 0;           // After all constraints
    SINT32 i;
    const SINT32 *mmi_voltageline = NULL;
    char cmd[64] = {0};

    static UINT32 s_last_reported_bat_lev_for_display = 0;
    static BOOL s_last_L1_was_on = FALSE;
    static BOOL s_is_first_call = TRUE;

    static UINT32 s_actual_prev_bat_lev_for_smoothing = 0;
    static BOOL s_actual_prev_charging_status_for_smoothing = FALSE;

    static UINT32 s_target_bat_lev_after_transition = 0;
    static BOOL s_in_catch_up_mode = TRUE;

    slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Entry V=%d, ChgStat=%d, PrevSmoothLev=%u, PrevSmoothChgStat=%d, CatchUp=%d\n",
         voltagepower, charging_status, s_actual_prev_bat_lev_for_smoothing, s_actual_prev_charging_status_for_smoothing, s_in_catch_up_mode);

    if (charging_status) {
        mmi_voltageline = g_mmi_chgvoltageline;
    } else {
        mmi_voltageline = g_mmi_batvoltageline;
    }

    if (mmi_voltageline == NULL || MMI_BAT_VOLTAGE_LEN < 2) {
        slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Error - voltage table invalid.\n");
        if (!s_is_first_call) return (SINT32)s_actual_prev_bat_lev_for_smoothing;
        return 0;
    }

    // 1. Calculate raw_bat_lev
    // ... (Calculation unchanged) ...
    if (voltagepower < mmi_voltageline[0]) {
        raw_bat_lev = 0;
    } else if (voltagepower >= mmi_voltageline[MMI_BAT_VOLTAGE_LEN - 1]) {
        raw_bat_lev = 100;
    } else {
        for (i = 0; i < MMI_BAT_VOLTAGE_LEN - 1; i++) {
            SINT32 v_low_threshold = mmi_voltageline[i];
            SINT32 v_high_threshold = mmi_voltageline[i + 1];
            if (voltagepower >= v_low_threshold && voltagepower < v_high_threshold) {
                double percent_low = (double)(i * 5);
                double percent_high = (double)((i + 1) * 5);
                double voltage_range_in_segment = (double)v_high_threshold - v_low_threshold;
                double voltage_offset_in_segment = (double)voltagepower - v_low_threshold;

                if (voltage_range_in_segment <= 0) {
                    raw_bat_lev = (UINT32)round(percent_low);
                } else {
                    double interpolated_percentage = percent_low +
                                                     (voltage_offset_in_segment / voltage_range_in_segment) * (percent_high - percent_low);
                    raw_bat_lev = (UINT32)round(interpolated_percentage);
                }
                if (raw_bat_lev > 100) raw_bat_lev = 100;
                if (raw_bat_lev < (UINT32)round(percent_low)) raw_bat_lev = (UINT32)round(percent_low);
                break;
            }
        }
    }
    slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: RawBatLev=%u\n", raw_bat_lev);

    // 2. Determine tentative_final_bat_lev using smoothing/catch-up logic
    if (s_is_first_call) {
        tentative_final_bat_lev = raw_bat_lev;
        //s_in_catch_up_mode = FALSE;
    } else {
        if (charging_status != s_actual_prev_charging_status_for_smoothing) {
            // --- Charging status has changed for smoothing logic! ---
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: ChgStat changed %d->%d.\n", s_actual_prev_charging_status_for_smoothing, charging_status);
            tentative_final_bat_lev = s_actual_prev_bat_lev_for_smoothing; // Freeze
            s_target_bat_lev_after_transition = raw_bat_lev; // Target is the raw new value
            //s_in_catch_up_mode = TRUE;
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: Transition! TentativeLev=%u (frozen), TargetLev=%u. EnterCatchUp.\n", tentative_final_bat_lev, s_target_bat_lev_after_transition);
        } else if (s_in_catch_up_mode) {
            // --- Still in the same charging state (for smoothing), but in catch-up mode ---
            s_target_bat_lev_after_transition = raw_bat_lev; // Update target
            SINT32 diff_to_target = (SINT32)s_target_bat_lev_after_transition - (SINT32)s_actual_prev_bat_lev_for_smoothing;
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: CatchUp. PrevSmoothLev=%u, TargetLev=%u, Diff=%d\n",
                 s_actual_prev_bat_lev_for_smoothing, s_target_bat_lev_after_transition, diff_to_target);

            if (abs(diff_to_target) <= MAX_PERCENT_CHANGE_PER_CALL) {
                tentative_final_bat_lev = s_target_bat_lev_after_transition;
                //s_in_catch_up_mode = FALSE;
                slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: TargetReached. TentativeLev=%u. ExitCatchUp.\n", tentative_final_bat_lev);
            } else if (diff_to_target > 0) {
                tentative_final_bat_lev = s_actual_prev_bat_lev_for_smoothing + MAX_PERCENT_CHANGE_PER_CALL;
                slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: CatchingUp (+). TentativeLev=%u\n", tentative_final_bat_lev);
            } else { // diff_to_target < 0
                tentative_final_bat_lev = s_actual_prev_bat_lev_for_smoothing - MAX_PERCENT_CHANGE_PER_CALL;
                slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: CatchingUp (-). TentativeLev=%u\n", tentative_final_bat_lev);
            }
        } else {
            // --- Charging status (for smoothing) has not changed, and not in catch-up mode ---
            tentative_final_bat_lev = raw_bat_lev; // Normal operation
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Smooth: Normal. TentativeLev=%u\n", tentative_final_bat_lev);
        }
    }

    // 3. Apply strict directional constraint (cannot go wrong way from previous)
    if (s_is_first_call) {
        final_bat_lev = tentative_final_bat_lev;
    } else {
        if (charging_status == TRUE) { // Currently charging
            final_bat_lev = MAX(tentative_final_bat_lev, s_actual_prev_bat_lev_for_smoothing);
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Constraint: Charging. Tentative=%u, PrevSmooth=%u -> Final=%u\n", tentative_final_bat_lev, s_actual_prev_bat_lev_for_smoothing, final_bat_lev);
        } else { // Currently NOT charging
            final_bat_lev = MIN(tentative_final_bat_lev, s_actual_prev_bat_lev_for_smoothing);
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Constraint: NotCharging. Tentative=%u, PrevSmooth=%u -> Final=%u\n", tentative_final_bat_lev, s_actual_prev_bat_lev_for_smoothing, final_bat_lev);
        }
    }

    // 4. Clamp final_bat_lev to 0-100
    if (final_bat_lev > 100) final_bat_lev = 100;
    // final_bat_lev is UINT32, so already >= 0.
    slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Clamped0-100 FinalBatLev=%u\n", final_bat_lev);

    // 5. Override with external charge status if available (e.g., STATE_FULL from PMIC)
    E_zMmi_Charge_State chg_sta = mmi_get_charge_status();
    if (chg_sta == STATE_FULL) {
        // If charger IC says "full", override to 100%.
        // This respects "charging only increases" if final_bat_lev was < 100.
        final_bat_lev = 100;
        slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: External STATE_FULL. FinalBatLev forced to 100.\n");
    }

#ifdef LED_DIGITS_DISPLAY
    // 6. Display charging icon (L1) logic
    // 当电量到100 可能是升压后到的，拔掉充电适配器，立马回掉下来会体验不好，这里还是充电芯片充满中断(需要确认硬件是否支持)
    // BOOL current_L1_should_be_on = (charging_status && final_bat_lev < 100);
    BOOL current_L1_should_be_on = (charging_status && chg_sta != STATE_FULL); // L1 off if full

    if (s_is_first_call || current_L1_should_be_on != s_last_L1_was_on) {
        if (current_L1_should_be_on) {
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Display: L1 ON (ChgStat=%d, ChgStaFull=%d, FinalBatt=%u%%)\n", charging_status, (chg_sta == STATE_FULL), final_bat_lev);
            system("echo L1ON > /dev/charlieplex_display");
        } else {
            slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Display: L1 OFF (ChgStat=%d, ChgStaFull=%d, FinalBatt=%u%%)\n", charging_status, (chg_sta == STATE_FULL), final_bat_lev);
            system("echo L1OFF > /dev/charlieplex_display");
        }
        s_last_L1_was_on = current_L1_should_be_on;
    }

    // 7. Display digit (percentage) logic
    if (s_is_first_call || final_bat_lev != s_last_reported_bat_lev_for_display) {
        slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI: Display: Percentage %u\n", final_bat_lev);
        snprintf(cmd, sizeof(cmd), "echo %u > /dev/charlieplex_display", final_bat_lev);
        system(cmd);
        // The L2ON logic was conditional in your previous version, make sure this is desired.
        // If L2ON should always follow a digit display update:

        // % 符号 只需要显示一次 后面都一样
        if (s_is_first_call) {
            //sleep(1); // Assuming sleep is desired after digit update
            system("echo L2ON > /dev/charlieplex_display");
        }
        
        s_last_reported_bat_lev_for_display = final_bat_lev;
    }
#endif

    // 8. Update state variables for the *next* call's smoothing logic
    s_actual_prev_bat_lev_for_smoothing = final_bat_lev;
    s_actual_prev_charging_status_for_smoothing = charging_status;
    if (s_is_first_call) {
        s_is_first_call = FALSE;
    }

 



    return (SINT32)final_bat_lev;
#endif
}
#else
SINT32 get_charging_voltage_level_from_table(SINT32 voltagepower, BOOL charging_status)
{
        UINT32 bat_lev = 0;
        SINT32 i;
        //fix bug
        //SINT32 (*mmi_voltageline)[MMI_BAT_VOLTAGE_LEN] = NULL;
        const SINT32 *mmi_voltageline = NULL; // Use const as the table contents are not modified here

        if (charging_status) {
            mmi_voltageline = g_mmi_chgvoltageline;
        } else {
            mmi_voltageline = g_mmi_batvoltageline;
        }

        if (voltagepower < mmi_voltageline[0]) {
                bat_lev = 0;
        } else if (voltagepower >= mmi_voltageline[MMI_BAT_VOLTAGE_LEN - 1]) {
                bat_lev = 100;
        } else {
                for (i = 0; i < MMI_BAT_VOLTAGE_LEN - 1; i++) {
                        if (voltagepower >= mmi_voltageline[i] && voltagepower < mmi_voltageline[i + 1]) {
                                bat_lev = i * 5;
                                break;
                        }
                }
        }
        return bat_lev;
}
#endif
#endif

/**********************************************************************************
*����˵������ȡ���״̬
 ***********************************************************************************/
E_zMmi_Charge_State mmi_get_charge_status(VOID)
{
	char chg_state_buf[CHARGE_STATUS_LENGTH] = {0};
	int len = 0;
	UINT32 i = 0;
	FILE* fd_charger = NULL;

	fd_charger = fopen(CHARGE_STATUS_PATH, "r");
	if (fd_charger == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_get_charge_status open charging file fail!\n");
		//MMI_ASSERT(0);
		return STATE_CHARGE_MAX;
	}
	len = fread(chg_state_buf, 1, CHARGE_STATUS_LENGTH, fd_charger);
	if (len > 0) { //kw 3
		fclose(fd_charger);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_get_charge_status chg_state_buf=%s !\n", chg_state_buf);
		for (i = 0; i < sizeof(g_chgStateStringTab) / sizeof(T_zMmiChgStateStringItem); ++ i) {
			if (strncmp(chg_state_buf, g_chgStateStringTab[i].devString, strlen(g_chgStateStringTab[i].devString)) == 0) {
				return g_chgStateStringTab[i].chg_sta;
			}
		}
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_get_charge_status read voltage file fail len = %d !\n", len);
		fclose(fd_charger);
		return STATE_CHARGE_MAX;
	}
	return STATE_CHARGE_MAX;
}
/**********************************************************************************
*����˵������ȡUSB���״̬
 ***********************************************************************************/
BOOL mmi_read_usb_insert_status(VOID)
{
	char usb_state_buf[2] = {0};
	int len = 0;
	FILE* fd_charger = NULL;

	fd_charger = fopen(USB_INSERT_STATUS_PATH, "r");
	if (fd_charger == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_read_usb_insert_status open usb file fail!\n");
		//MMI_ASSERT(0);
		return FALSE;
	}
	len = fread(usb_state_buf, 1, 2, fd_charger);
	if (len > 0) { //kw 3
		fclose(fd_charger);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_read_usb_insert_status usb_state_buf=%s !\n", usb_state_buf);
		if (!strncmp(usb_state_buf, USB_STATUS_IN, strlen(USB_STATUS_IN))) {
			return TRUE;
		}
		if (!strncmp(usb_state_buf, USB_STATUS_OUT, strlen(USB_STATUS_OUT))) {
			return FALSE;
		}
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_read_usb_insert_status read usb file fail len = %d !\n", len);
		fclose(fd_charger);
		return FALSE;
	}
	return FALSE;
}

