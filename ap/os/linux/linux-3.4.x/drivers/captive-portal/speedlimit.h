#ifndef __SPEEDLIMIT_H__
#define __SPEEDLIMIT_H__

#define MAX_WHITELIST_IP 20

#define NIPQUAD(addr) \
  ((unsigned char *)&addr)[0], \
  ((unsigned char *)&addr)[1], \
  ((unsigned char *)&addr)[2], \
  ((unsigned char *)&addr)[3]

typedef union {
    int int_ip;
    char char_ip[4];
} IPADR;

struct mac_int {
    int mac1;
    short mac2;
};

struct mac_char {
    char mac[6];
};

typedef union {
    struct mac_int mac_int;
    struct mac_char mac_char;
} MACADR;

typedef struct {
    IPADR ip;
    time_t tm;
} IP_LIST;

typedef struct {
    IPADR ip;
    MACADR mac;
    time_t time;
} CLIENT;

typedef struct client_list {
    struct client_list *prev;
    struct client_list *next;
    CLIENT client;
} CLIENT_LIST;

typedef struct redir {
    unsigned short port;
    int ip;
    time_t time;
} REDDIR;

struct psd_header {

    unsigned long saddr; //Դ��ַ

    unsigned long daddr; //Ŀ�ĵ�ַ

    char mbz;//�ÿ�

    char ptcl; //Э������

    unsigned short tcpl; //TCP����

};


//#define WLANDEVICE ("wlan0")
#define WLANDEVICE ("br0")
#define LOCALGATEWAYPORT 9000
#define DEFAULTHTTPPORT 80
#define HTTPS_PPORT 443
// Enable this macro to support IPv6 portal functionality
#define CONFIG_IPV6_PORTAL
#define CONFIG_DNS_REDIRECT

#ifdef CONFIG_IPV6_PORTAL
#include <linux/in6.h>

// IPv6 address structure for portal use
typedef struct {
    struct in6_addr ipv6;
    time_t tm;
} IPV6_LIST;

// Extended client structure to support both IPv4 and IPv6
typedef struct {
    IPADR ip;           // IPv4 address (for backward compatibility)
    struct in6_addr ipv6;  // IPv6 address
    MACADR mac;
    time_t time;
    int is_ipv6;        // Flag to indicate if this is an IPv6 client
} CLIENT_V6;

typedef struct client_list_v6 {
    struct client_list_v6 *prev;
    struct client_list_v6 *next;
    CLIENT_V6 client;
} CLIENT_LIST_V6;

// IPv6 redirect structure
typedef struct redir_v6 {
    unsigned short port;
    struct in6_addr ipv6;
    time_t time;
} REDDIR_V6;

// IPv6 pseudo header for checksum calculation
struct ipv6_psd_header {
    struct in6_addr saddr;
    struct in6_addr daddr;
    __be32 len;
    __be32 nexthdr;
};

extern void init_ipv6_whitelist(void);
extern int add_ipv6_whitelist(const char *ipv6_str);
extern int add_ipv6_whitelist_raw(u32 addr0, u32 addr1, u32 addr2, u32 addr3);
extern void clear_ipv6_whitelist(void);
#endif

extern int mytime(void);

// IPv4 白名单管理函数
extern int add_ipv4_whitelist(const char *ip_str);
extern void clear_ipv4_whitelist(void);
extern void init_ipv4_whitelist(void);
extern __be32 whitelist_ipv4[MAX_WHITELIST_IP];
extern int whitelist_ipv4_count;

// Add these function declarations if they don't exist
extern void clearAuthList(void);
extern void move2NewList(CLIENT_LIST *cli);
extern CLIENT_LIST *authClient;
extern CLIENT_LIST *newClient;

#endif
