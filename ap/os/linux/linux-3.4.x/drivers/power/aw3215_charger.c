/*
 * Driver for the TI zx234502 battery charger.
 *
 * Author: <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */
#define DEBUG
#include <linux/module.h>
#include <linux/interrupt.h>
#include <linux/delay.h>
#include <linux/of_irq.h>
#include <linux/of_device.h>
#include <linux/pm_runtime.h>
#include <linux/power_supply.h>
#include <linux/gpio.h>
#include <linux/i2c.h>
#include <linux/irq.h>
#include <linux/kthread.h>
//#include <linux/mutex.h>
#include <linux/semaphore.h>

#include <linux/power/aw3215_charger.h>
#include <linux/mfd/zx234290.h>

#include <mach/gpio.h>
#include <mach/pcu.h>
#include <mach/zx29_usb.h>
#include <linux/workqueue.h>

#include <linux/slab.h>
#include <linux/debugfs.h>
#include <asm/uaccess.h>
#include <linux/wakelock.h>

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
#include <linux/jiffies.h>
#endif

#define CHG_DEBUG
#ifdef CHG_DEBUG
#define GIC_DIST_ENABLE_SET		0x100
#define GIC_DIST_PENDING_SET	0x200
extern void __iomem *base_testtt;
#endif

static volatile int s_chg_chin_cnt = 0;
static volatile int s_chg_chstate_cnt = 0;

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
/* Battery capacity table for smooth level calculation */
struct capacity {
	int capacity;
	int min;
	int max;
	int offset;
	int hysteresis;
};

/* Battery capacity table for 10000mAh battery based on 25°C OCV test data */
static struct capacity battery_capacity_tables_10000mah[] = {
	/* Based on actual OCV measurements from test data */
	{0,  3100, 3348, 0, 10},  /* 0% - 3348mV (lowest measured) */
	{1,  3349, 3408, 0, 10},  /* 1% - 3408mV */
	{2,  3409, 3456, 0, 10},  /* 2% - 3456mV */
	{3,  3457, 3492, 0, 10},  /* 3% - 3492mV */
	{4,  3493, 3513, 0, 10},  /* 4% - 3513mV */
	{5,  3514, 3525, 0, 10},  /* 5% - 3525mV */
	{10, 3526, 3576, 0, 10},  /* 10% - 3576mV */
	{15, 3577, 3625, 0, 10},  /* 15% - 3625mV */
	{20, 3626, 3677, 0, 10},  /* 20% - 3677mV */
	{25, 3678, 3723, 0, 10},  /* 25% - 3723mV */
	{30, 3724, 3758, 0, 10},  /* 30% - 3758mV */
	{35, 3759, 3792, 0, 10},  /* 35% - 3792mV */
	{40, 3793, 3820, 0, 10},  /* 40% - 3820mV */
	{45, 3821, 3844, 0, 10},  /* 45% - 3844mV */
	{50, 3845, 3860, 0, 10},  /* 50% - 3860mV */
	{55, 3861, 3875, 0, 10},  /* 55% - 3875mV */
	{60, 3876, 3893, 0, 10},  /* 60% - 3893mV */
	{65, 3894, 3915, 0, 10},  /* 65% - 3915mV */
	{70, 3916, 3939, 0, 10},  /* 70% - 3939mV */
	{75, 3940, 3963, 0, 10},  /* 75% - 3963mV */
	{80, 3964, 3991, 0, 10},  /* 80% - 3991mV */
	{85, 3992, 4013, 0, 10},  /* 85% - 4013mV */
	{90, 4014, 4043, 0, 10},  /* 90% - 4043mV */
	{95, 4044, 4097, 0, 10},  /* 95% - 4097mV */
	{98, 4098, 4131, 0, 10},  /* 98% - 4131mV */
	{99, 4132, 4153, 0, 10},  /* 99% - 4153mV */
	{100,4154, 4500, 0, 10},  /* 100% - 4196mV+ */
};

#define BATTERY_CAPACITY_TABLE_SIZE ARRAY_SIZE(battery_capacity_tables_10000mah)

/* Battery smooth level control parameters for 10000mAh battery */
#define BATTERY_DISCHARGE_TIME_SEC (4.5 * 60 * 60)  /* 4.5 hours in seconds */
#define BATTERY_LEVEL_CHANGE_INTERVAL_MS 10000       /* 10 seconds */
#define MAX_LEVEL_CHANGE_PER_INTERVAL 1              /* Max 1% change per interval */
#define BATTERY_CAPACITY_MAH 10000                   /* Battery capacity in mAh */

/* Calibration correction parameters */
#define CALIBRATION_WAIT_TIME_MS (5 * 60 * 1000)    /* Wait time when real capacity is lower than displayed */
#define CALIBRATION_SLOW_INCREASE_RATE 5             /* Very slow increase: 1% per 50 seconds when waiting for real capacity */

/* Battery calibration parameters */
#define BATTERY_CALIBRATION_INTERVAL_MS (2 * 60 * 1000)  /* 5 minutes */
#define BATTERY_CALIBRATION_LEVEL_THRESHOLD 5             /* 5% level increase */
#define BATTERY_CALIBRATION_DISABLE_TIME_MS 2000          /* 5 seconds disable time for voltage stabilization */
#define BATTERY_MIN_SAFE_VOLTAGE_MV 3400                  /* Minimum safe voltage to disable charging */

/* Battery level smoothing structure */
struct battery_smooth_level {
	int current_level;
	int target_level;
	unsigned long last_update_time;
	int charging_state;
	struct mutex level_mutex;
	int calibration_offset;  /* User calibration offset */
	bool calibration_set;    /* Whether calibration has been set */

	/* Calibration control */
	unsigned long last_calibration_time;
	int last_calibration_level;
	bool calibration_in_progress;
	struct delayed_work calibration_work;
	struct delayed_work restore_charging_work;
	int real_chg_en;  /* Real charging state (not affected by calibration) */
	bool suppress_power_supply_change; /* Suppress power supply change events during calibration */

	/* Calibration correction control */
	bool calibration_wait_active;  /* True when waiting for real capacity to catch up */
	unsigned long calibration_wait_start_time;  /* When wait started */
	int calibration_real_level;  /* Real level from calibration */
	int calibration_displayed_level;   /* Displayed level to wait at */
};

/* Module parameter for battery calibration */
static int battery_calibration_level = -1;
module_param(battery_calibration_level, int, 0644);
MODULE_PARM_DESC(battery_calibration_level, "Battery calibration level (0-100), -1 to disable");

/* Module parameter for battery calibration enabled */
static int battery_calibration_enabled = 1;
module_param(battery_calibration_enabled, int, 0644);
MODULE_PARM_DESC(battery_calibration_enabled, "Battery calibration enabled (0,1,0xFF), 0 to disable, 1 to enabled, 0xFF to force calibration");

/* Forward declarations */
static void aw3215_calibration_work_func(struct work_struct *work);
static void aw3215_restore_charging_work_func(struct work_struct *work);
#endif


#define USB_IN		GPIO_LOW
#define USB_OUT		GPIO_HIGH
#define CHG_START	GPIO_LOW
#define CHG_STOP	GPIO_HIGH
#ifdef _USE_V3PHONE_TYPE_XRSD_
#define CHG_EN_TYPE		GPIO_HIGH
#define CHG_DISEN_TYPE	GPIO_LOW
#else
#define CHG_EN_TYPE		GPIO_LOW
#define CHG_DISEN_TYPE	GPIO_HIGH
#endif

//*when the bat voltage <3.2V usb will not enum ,then the sys fall in lowpower*/
#define USB_ENUM_MIN	3400

/*
 * The FAULT register is latched by the zx234502 (except for NTC_FAULT)
 * so the first read after a fault returns the latched value and subsequent
 * reads return the current value.  In order to return the fault status
 * to the user, have the interrupt handler save the reg's value and retrieve
 * it in the appropriate health/status routine.  Each routine has its own
 * flag indicating whether it should use the value stored by the last run
 * of the interrupt handler or do an actual reg read.  That way each routine
 * can report back whatever fault may have occured.
 */
 enum chg_stop_reason{
	CHG_STOP_REASON_NO = 0,
	CHG_STOP_REASON_TEMP= 1,
	CHG_STOP_REASON_FULL = 2,
	CHG_STOP_DEFALT= 0xff
};
struct aw3215_dev_info {
	struct device		*dev;
	struct power_supply	charger;
	struct power_supply	battery;

	kernel_ulong_t		model;
	unsigned int		chgin_irq;
	unsigned int		chgstate_irq;
	//struct task_struct	*chgin_irq_thread;
	struct task_struct	*chgstate_irq_thread;

	u8					watchdog;

	struct aw3215_platform_data 	*pdata;
	unsigned int					chgin_type;
	unsigned int					chgstate_type;
	bool							chg_en;
	unsigned int					chg_state;
	//struct semaphore		chgin_sem;
	struct semaphore		chgstate_sem;
	enum chg_stop_reason	stopchg_flag;
	struct timer_list		changed_timer;
	struct wake_lock		wlock_chgfull;

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	struct battery_smooth_level	smooth_level;
#endif
};

struct aw3215_dev_info *g_bdi = NULL;

/* Charger power supply property routines */

static int aw3215_charger_get_charge_type(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
	val->intval = bdi->charger.type;
	return 0;
}

static int aw3215_charger_get_status(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
#if 1
	val->intval = bdi->chg_state;
#else
	if(false==bdi->chg_en){
		val->intval=POWER_SUPPLY_STATUS_NOT_CHARGING;/*diaable chg*/
		return 0;
	}
	if (USB_IN==bdi->chgin_type) {
		if(CHG_STOP==bdi->chgstate_type)
			val->intval= POWER_SUPPLY_STATUS_FULL;
		else if (CHG_START==bdi->chgstate_type)
			val->intval= POWER_SUPPLY_STATUS_CHARGING;
		else
			val->intval=POWER_SUPPLY_STATUS_NOT_CHARGING;/*diaable chg*/
	} else {
		val->intval=POWER_SUPPLY_STATUS_DISCHARGING;/*usb not insert*/
	}
#endif
	return 0;
}

static int aw3215_charger_get_health(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
	val->intval = POWER_SUPPLY_HEALTH_GOOD;

	return 0;
}

int aw3215_charger_get_online(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
	if (USB_IN==bdi->chgin_type) {
		val->intval= 1;
	} else {
		val->intval=0;/*usb not insert*/
	}

	return 0;
}EXPORT_SYMBOL (aw3215_charger_get_online);


static int aw3215_charger_get_charger_enabled(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	/* During calibration, return the real charging state instead of temporary disabled state */
	if (bdi->smooth_level.calibration_in_progress && battery_calibration_enabled) {
		val->intval = bdi->smooth_level.real_chg_en;
		printk(KERN_DEBUG "aw3215: get_charger_enabled during calibration: returning real_state=%d\n",
			   bdi->smooth_level.real_chg_en);
	} else {
		val->intval = bdi->chg_en;
	}
#else
	val->intval = bdi->chg_en;
#endif

	return 0;
}
static int aw3215_charger_get_voltage_max(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
	val->intval = 4200;

	return 0;
}

static int aw3215_charger_set_voltage(struct aw3215_dev_info *bdi,const union power_supply_propval *val)
{
	return 0;
}
static int aw3215_charger_set_charger_config(struct aw3215_dev_info *bdi,const union power_supply_propval *val)
{
	int ret = 0;
	int gpio_state = 0;

	if (val->intval==1) {
		if (bdi->chg_en)
			return 0;
		gpio_state = CHG_EN_TYPE ;/*gpio low en chg*/
		printk("mmi start chg\n");
	} else {
		if (!bdi->chg_en)
			return 0;
		gpio_state = CHG_DISEN_TYPE ;/*gpio high stop chg*/
		printk("mmi stop chg\n");
	}

	disable_irq(bdi->chgstate_irq);

	if (gpio_state == CHG_EN_TYPE) {
		bdi->chg_en = true;		/*(~gpio_state)*/
		bdi->chgstate_type = CHG_START;
		bdi->stopchg_flag = CHG_STOP_REASON_NO;
		gpio_set_value(bdi->pdata->gpio_chgen, gpio_state);

		if (bdi->chgin_type == USB_IN) {	
			bdi->chg_state = POWER_SUPPLY_STATUS_CHARGING;		
			irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);
			/* start charging in 5.3ms after enable */
			if (gpio_get_value(bdi->pdata->gpio_chgstate))
				mdelay(40);
			if (gpio_get_value(bdi->pdata->gpio_chgstate)){
				printk(KERN_INFO "chg still not chargin\n"); /* should not go here */	
				bdi->chg_state = POWER_SUPPLY_STATUS_FULL;			
				irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_LOW);
			}

		}
	} else {
		bdi->chg_en = false;
		bdi->chgstate_type = CHG_STOP;
		bdi->stopchg_flag = CHG_STOP_REASON_TEMP;
		if (2==val->intval){/*chg full stop*/
			bdi->stopchg_flag = CHG_STOP_REASON_FULL;
			bdi->chg_state = POWER_SUPPLY_STATUS_FULL;
			printk("mmi full stop chg\n");
	
		}
		else if (bdi->chgin_type == USB_IN)
			bdi->chg_state = POWER_SUPPLY_STATUS_NOT_CHARGING;

			gpio_set_value(bdi->pdata->gpio_chgen, gpio_state);
		/* charger state changes from 0 to 1 in 0.12ms */
			if (!gpio_get_value(bdi->pdata->gpio_chgstate))
				udelay(500);

			irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_LOW);

	}

	enable_irq(bdi->chgstate_irq);
#ifdef CHG_DEBUG
		printk("irq1 chg_in= %d,chg_state_cnt= %d\n",s_chg_chstate_cnt,s_chg_chin_cnt);
		printk("chg int enable reg1 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_ENABLE_SET+0x4));
		printk("chg int enable reg2 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_ENABLE_SET+0x8));
		printk("chg int pending reg1 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_PENDING_SET+0x4));
		printk("chg int pending reg2 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_PENDING_SET+0x8));
		printk("irq2 chg_in= %d,chg_state_cnt= %d\n",s_chg_chstate_cnt,s_chg_chin_cnt);
#endif

	power_supply_changed(&bdi->charger);

	return ret;
}


static int aw3215_charger_get_property(struct power_supply *psy,enum power_supply_property psp, union power_supply_propval *val)
{
	struct aw3215_dev_info *bdi = container_of(psy, struct aw3215_dev_info, charger);
	int ret;

	//dev_dbg(bdi->dev, "prop: %d\n", psp);

	//pm_runtime_get_sync(bdi->dev);

	switch (psp) {
	case POWER_SUPPLY_PROP_PC1_AC2:
		ret = aw3215_charger_get_charge_type(bdi, val);
		break;

	case POWER_SUPPLY_PROP_STATUS:
		ret = aw3215_charger_get_status(bdi, val);
		break;
	case POWER_SUPPLY_PROP_HEALTH:
		ret = aw3215_charger_get_health(bdi, val);
		break;
	case POWER_SUPPLY_PROP_ONLINE:
		ret = aw3215_charger_get_online(bdi, val);
		break;

	case POWER_SUPPLY_PROP_VOLTAGE_MAX:
		ret = aw3215_charger_get_voltage_max(bdi, val);
		break;

	case POWER_SUPPLY_PROP_CHARGE_ENABLED:
		ret = aw3215_charger_get_charger_enabled(bdi, val);
		break;
	default:
		ret = -ENODATA;
	}

	//pm_runtime_put_sync(bdi->dev);
	return ret;
}

static int aw3215_charger_set_property(struct power_supply *psy,enum power_supply_property psp,
		const union power_supply_propval *val)
{
	struct aw3215_dev_info *bdi =
			container_of(psy, struct aw3215_dev_info, charger);
	int ret;

	//dev_dbg(bdi->dev, "prop: %d\n", psp);

	//pm_runtime_get_sync(bdi->dev);

	switch (psp) {
#if 0
	case POWER_SUPPLY_PROP_CURRENT_NOW:
		ret = zx234502_charger_set_current(bdi, val);
		break;
#endif
	case POWER_SUPPLY_PROP_VOLTAGE_MAX:
		ret = aw3215_charger_set_voltage(bdi, val);
		break;

    case POWER_SUPPLY_PROP_CHARGE_ENABLED:
        ret = aw3215_charger_set_charger_config(bdi, val);
        break;
	default:
		ret = -EINVAL;
	}

	//pm_runtime_put_sync(bdi->dev);
	return ret;
}

static int aw3215_charger_property_is_writeable(struct power_supply *psy,enum power_supply_property psp)
{
	int ret;

	switch (psp)
	{
    	//case POWER_SUPPLY_PROP_CURRENT_NOW:
    	case POWER_SUPPLY_PROP_VOLTAGE_MAX:
	case POWER_SUPPLY_PROP_CHARGE_ENABLED:
    		ret = 1;
    		break;
	default:
		ret = 0;
		break;
	}

	return ret;
}

static enum power_supply_property aw3215_charger_properties[] = {
	POWER_SUPPLY_PROP_PC1_AC2,
	POWER_SUPPLY_PROP_STATUS,
	POWER_SUPPLY_PROP_HEALTH,
	POWER_SUPPLY_PROP_ONLINE,
	//POWER_SUPPLY_PROP_CURRENT_NOW,
	//POWER_SUPPLY_PROP_CURRENT_MAX,
	//POWER_SUPPLY_PROP_VOLTAGE_NOW,
	POWER_SUPPLY_PROP_VOLTAGE_MAX,
	POWER_SUPPLY_PROP_CHARGE_ENABLED,
};

static char *aw3215_charger_supplied_to[] = {
	"main-battery",
};

static void aw3215_charger_init(struct power_supply *charger)
{
	charger->name = "charger";
	charger->type = POWER_SUPPLY_PCAC_UNKNOWN;
	charger->properties = aw3215_charger_properties;
	charger->num_properties = ARRAY_SIZE(aw3215_charger_properties);
	charger->supplied_to = aw3215_charger_supplied_to;
	//charger->num_supplies = ARRAY_SIZE(aw3215_charger_supplied_to);
	charger->get_property = aw3215_charger_get_property;
	charger->set_property = aw3215_charger_set_property;
	charger->property_is_writeable = aw3215_charger_property_is_writeable;
}

/* Battery power supply property routines */

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
/* Calculate battery capacity from voltage using lookup table */
static int aw3215_calculate_capacity_from_voltage(int voltage_mv)
{
	struct capacity *tables = battery_capacity_tables_10000mah;
	int high = BATTERY_CAPACITY_TABLE_SIZE - 1;
	int low = 0;
	int mid;

	if (voltage_mv <= tables[0].min) {
		return tables[0].capacity;
	}

	if (voltage_mv >= tables[high].max) {
		return tables[high].capacity;
	}

	/* Binary search for the appropriate capacity range */
	while (high >= low) {
		mid = (high + low) / 2;

		if (voltage_mv < tables[mid].min) {
			high = mid - 1;
		} else if (voltage_mv > tables[mid].max) {
			low = mid + 1;
		} else {
			/* Voltage is within this range */
			return tables[mid].capacity;
		}
	}

	/* If not found exactly, interpolate between adjacent ranges */
	if (low < BATTERY_CAPACITY_TABLE_SIZE) {
		return tables[low].capacity;
	}

	return tables[high].capacity;
}

/* Initialize smooth battery level control */
static void aw3215_init_smooth_level(struct aw3215_dev_info *bdi)
{
	int voltage_mv, initial_capacity;
	bool was_charging = false;

	/* Initialize mutex first */
	mutex_init(&bdi->smooth_level.level_mutex);

	/* Initialize calibration control */
	bdi->smooth_level.calibration_in_progress = false;
	bdi->smooth_level.suppress_power_supply_change = false;
	bdi->smooth_level.calibration_offset = 0;
	bdi->smooth_level.calibration_set = false;

	/* Initialize calibration correction control */
	bdi->smooth_level.calibration_wait_active = false;
	bdi->smooth_level.calibration_wait_start_time = 0;
	bdi->smooth_level.calibration_real_level = 0;
	bdi->smooth_level.calibration_displayed_level = 0;

	/* Initialize work queues */
	INIT_DELAYED_WORK(&bdi->smooth_level.calibration_work, aw3215_calibration_work_func);
	INIT_DELAYED_WORK(&bdi->smooth_level.restore_charging_work, aw3215_restore_charging_work_func);

	/* Check if currently charging and temporarily disable it to get accurate voltage */
	if (bdi->chg_en && bdi->chgin_type == USB_IN) {
		/* First check current voltage to ensure it's safe to disable charging */
		int current_voltage = get_adc1_voltage() - 15;

		/* Only disable charging if voltage is above minimum safe level to prevent shutdown */
		if (current_voltage > BATTERY_MIN_SAFE_VOLTAGE_MV && battery_calibration_enabled) {
			was_charging = true;
			bdi->smooth_level.suppress_power_supply_change = true; /* Suppress events during init */

			/* Temporarily disable charging */
			gpio_set_value(bdi->pdata->gpio_chgen, CHG_DISEN_TYPE);
			printk(KERN_DEBUG "aw3215: temporarily disabled charging for accurate init measurement (voltage=%dmV)\n", current_voltage);

			/* Wait for voltage to stabilize */
			msleep(BATTERY_CALIBRATION_DISABLE_TIME_MS);
		} else {
			printk(KERN_WARNING "aw3215: voltage too low (%dmV), skipping charging disable for init\n", current_voltage);
			/* Keep charging enabled, use current voltage reading (may be slightly high due to charging) */
		}
	}

	/* Get accurate voltage and calculate initial capacity */
	voltage_mv = get_adc1_voltage() - 15;
	initial_capacity = aw3215_calculate_capacity_from_voltage(voltage_mv);

	/* Restore charging if it was enabled */
	if (was_charging) {
		gpio_set_value(bdi->pdata->gpio_chgen, CHG_EN_TYPE);
		printk(KERN_DEBUG "aw3215: restored charging after init measurement\n");
		bdi->smooth_level.suppress_power_supply_change = false;
	}

	/* Initialize level values */
	bdi->smooth_level.current_level = initial_capacity;
	bdi->smooth_level.target_level = initial_capacity;
	bdi->smooth_level.last_update_time = jiffies;
	bdi->smooth_level.charging_state = (bdi->chg_state == POWER_SUPPLY_STATUS_CHARGING) ? 1 : 0;
	bdi->smooth_level.last_calibration_time = jiffies;
	bdi->smooth_level.last_calibration_level = initial_capacity;
	bdi->smooth_level.real_chg_en = bdi->chg_en;

	printk(KERN_INFO "aw3215: initialized battery level at %d%% (voltage=%dmV, was_charging=%s)\n",
		   initial_capacity, voltage_mv, was_charging ? "yes" : "no");
}



/* Wrapper function to suppress power supply change events during calibration */
static void aw3215_power_supply_changed_safe(struct aw3215_dev_info *bdi)
{
	if (!bdi->smooth_level.suppress_power_supply_change) {
		power_supply_changed(&bdi->charger);
		power_supply_changed(&bdi->battery);
	} else {
		printk(KERN_DEBUG "aw3215: suppressed power supply change event during calibration\n");
	}
}

/* Disable charging for calibration (internal function, no power supply change) */
static void aw3215_disable_charging_for_calibration(struct aw3215_dev_info *bdi)
{
	if (!bdi->smooth_level.calibration_in_progress) {
		bdi->smooth_level.calibration_in_progress = true;
		bdi->smooth_level.suppress_power_supply_change = true;

		/* Save real charging enable state */
		bdi->smooth_level.real_chg_en = (gpio_get_value(bdi->pdata->gpio_chgen) == CHG_EN_TYPE) ? 1 : 0;

		/* Disable charging */
		//if (bdi->chg_en) {
		gpio_set_value(bdi->pdata->gpio_chgen, CHG_DISEN_TYPE);
		//bdi->chg_en = false;
		printk(KERN_DEBUG "aw3215: charging disabled for calibration real_chg_en =%d, chg_en=%d\n", bdi->smooth_level.real_chg_en, ((gpio_get_value(bdi->pdata->gpio_chgen) == CHG_EN_TYPE) ? 1 : 0));
		//}
	}
}

/* Restore charging after calibration (internal function, no power supply change) */
static void aw3215_restore_charging_after_calibration(struct aw3215_dev_info *bdi)
{
	if (bdi->smooth_level.calibration_in_progress) {
		/* Restore charging state if it was enabled before */
		//if (bdi->smooth_level.real_chg_en) {
			gpio_set_value(bdi->pdata->gpio_chgen, CHG_EN_TYPE);
			//bdi->chg_en = true;
			printk(KERN_DEBUG "aw3215: charging restored after calibration\n");
		//}

		bdi->smooth_level.calibration_in_progress = false;
		bdi->smooth_level.suppress_power_supply_change = false;
	}
}

/* Calibration work function - disable charging temporarily and calculate real capacity */
static void aw3215_calibration_work_func(struct work_struct *work)
{
	struct battery_smooth_level *smooth_level = container_of(work, struct battery_smooth_level, calibration_work.work);
	struct aw3215_dev_info *bdi = container_of(smooth_level, struct aw3215_dev_info, smooth_level);
	int voltage_mv, real_capacity;

	printk(KERN_DEBUG "aw3215: starting calibration work\n");

	/* First check current voltage to ensure it's safe to disable charging */
	int pre_voltage = get_adc1_voltage() - 15;

	if (pre_voltage <= BATTERY_MIN_SAFE_VOLTAGE_MV) {
		printk(KERN_WARNING "aw3215: voltage too low (%dmV), aborting calibration to prevent shutdown\n", pre_voltage);
		return; /* Abort calibration, keep charging enabled */
	}

	/* Disable charging to get accurate voltage */
	aw3215_disable_charging_for_calibration(bdi);

	/* Wait for voltage to stabilize */
	msleep(BATTERY_CALIBRATION_DISABLE_TIME_MS);

	/* Get real voltage and calculate real capacity */
	voltage_mv = get_adc1_voltage() - 15;
	real_capacity = aw3215_calculate_capacity_from_voltage(voltage_mv);

	printk(KERN_INFO "aw3215: calibration - pre_voltage=%dmV(charging), voltage=%dmV, real_capacity=%d%%, current_level=%d%%\n",
		   pre_voltage, voltage_mv, real_capacity, bdi->smooth_level.current_level);

	/* Update the smooth level with calibrated value */
	mutex_lock(&bdi->smooth_level.level_mutex);

	/* Calculate calibration offset based on real measurement */
	int offset_change = real_capacity - bdi->smooth_level.current_level;

	/* Only apply significant calibration changes (>= 2%) to avoid noise */
	if (pre_voltage > voltage_mv && abs(offset_change) >= 2) {
		// 设置校准后的电量  和 用户手动校准一样的效果 battery_calibration_level
		battery_calibration_level = real_capacity;
		bdi->smooth_level.calibration_set = false;
		
		printk(KERN_INFO "aw3215: applied calibration offset %+d%%, new level=%d%%\n",
			   offset_change, real_capacity);
	} else {
		printk(KERN_DEBUG "aw3215: calibration offset too small (%+d%%), ignored\n", offset_change);
	}

	/* If calibrated capacity is 100%, set charging state to FULL */
	if (pre_voltage >= voltage_mv && battery_calibration_level >= 100 && real_capacity >= 100) {
		bdi->chg_state = POWER_SUPPLY_STATUS_FULL;
		printk(KERN_INFO "aw3215: battery is full (100%%), setting charge state to FULL\n");
	}

	
	mutex_unlock(&bdi->smooth_level.level_mutex);

	/* Immediately restore charging */
	aw3215_restore_charging_after_calibration(bdi);

	/* Schedule backup restore work as safety net (in case something goes wrong) */
	schedule_delayed_work(&bdi->smooth_level.restore_charging_work,
						  msecs_to_jiffies(200)); /* 200ms backup */

	printk(KERN_DEBUG "aw3215: calibration work completed\n");
}

/* Restore charging work function (backup safety net) */
static void aw3215_restore_charging_work_func(struct work_struct *work)
{
	struct battery_smooth_level *smooth_level = container_of(work, struct battery_smooth_level, restore_charging_work.work);
	struct aw3215_dev_info *bdi = container_of(smooth_level, struct aw3215_dev_info, smooth_level);

	/* Only restore if calibration is still in progress (safety net) */
	if (bdi->smooth_level.calibration_in_progress) {
		printk(KERN_WARNING "aw3215: backup restore charging work triggered - calibration stuck!\n");
		aw3215_restore_charging_after_calibration(bdi);
	} else {
		printk(KERN_DEBUG "aw3215: backup restore work - calibration already completed\n");
	}
}

/* Check if calibration is needed */
static bool aw3215_should_calibrate(struct aw3215_dev_info *bdi, int current_level)
{
	unsigned long current_time = jiffies;
	unsigned long time_diff_ms;
	int level_diff;
	int current_voltage;

	/* Don't calibrate if not charging */
	if (bdi->chg_state != POWER_SUPPLY_STATUS_CHARGING) {
		return false;
	}

	/* Don't calibrate if already in progress */
	if (bdi->smooth_level.calibration_in_progress) {
		return false;
	}

	/* Don't calibrate if voltage is too low (unsafe to disable charging) */
	current_voltage = get_adc1_voltage() - 15;
	if (current_voltage <= BATTERY_MIN_SAFE_VOLTAGE_MV) {
		printk(KERN_DEBUG "aw3215: skipping calibration due to low voltage (%dmV)\n", current_voltage);
		return false;
	}

	time_diff_ms = jiffies_to_msecs(current_time - bdi->smooth_level.last_calibration_time);
	level_diff = current_level - bdi->smooth_level.last_calibration_level;

	/* Calibrate if 5 minutes passed */
	if (time_diff_ms >= BATTERY_CALIBRATION_INTERVAL_MS) {
		return true;
	}

	return false;
}

/* Update smooth battery level based on current conditions */
static int aw3215_update_smooth_level(struct aw3215_dev_info *bdi)
{
	int voltage_mv = get_adc1_voltage() - 15;
	int raw_capacity = aw3215_calculate_capacity_from_voltage(voltage_mv);
	int is_charging;
	int calculated_target;

	/* More robust charging state detection */
	is_charging = (bdi->chg_state == POWER_SUPPLY_STATUS_CHARGING) ? 1 : 0;

	/* Additional validation: if USB is not connected, definitely not charging */
	if (bdi->chgin_type != USB_IN) {
		is_charging = 0;
	}
	unsigned long current_time = jiffies;
	unsigned long time_diff_ms;
	int level_change = 0;
	int result_level;

	printk(KERN_DEBUG "aw3215: %s enter is_charging=%d, voltage=%dmV, raw_capacity=%d%% \n", __func__, is_charging, voltage_mv, raw_capacity);

	mutex_lock(&bdi->smooth_level.level_mutex);

	/* Skip normal processing if calibration is in progress */
	if (bdi->smooth_level.calibration_in_progress) {
		result_level = bdi->smooth_level.current_level;
		mutex_unlock(&bdi->smooth_level.level_mutex);
		return result_level;
	}

	/* Check if user has set calibration */
	if (battery_calibration_level >= 0 && battery_calibration_level <= 100) {
		if (!bdi->smooth_level.calibration_set) {
			/* First time calibration is set */
			int offset_change = battery_calibration_level - bdi->smooth_level.current_level;

			if (offset_change < 0 && is_charging) {
				/* Real capacity is lower than displayed during charging - start wait mode */
				bdi->smooth_level.calibration_wait_active = true;
				bdi->smooth_level.calibration_wait_start_time = current_time;
				bdi->smooth_level.calibration_real_level = battery_calibration_level;
				bdi->smooth_level.calibration_displayed_level = bdi->smooth_level.current_level;
				bdi->smooth_level.calibration_offset = offset_change;
				bdi->smooth_level.calibration_set = true;

				printk(KERN_INFO "aw3215: charging wait mode - displayed=%d%%, real=%d%%, waiting for real capacity to catch up\n",
					   bdi->smooth_level.current_level, battery_calibration_level);
			} else if (!is_charging) {
				/* Discharging state - current voltage/capacity is accurate, ignore potentially inaccurate calibration value */
				/* Reset calibration value to current accurate level and clear any offset */
				battery_calibration_level = bdi->smooth_level.current_level;
				bdi->smooth_level.calibration_offset = 0;
				bdi->smooth_level.calibration_set = true;

				printk(KERN_INFO "aw3215: discharging calibration corrected - using accurate current level %d%% (was %d%%)\n",
					   bdi->smooth_level.current_level, battery_calibration_level);
			} else {
				/* Normal calibration - apply offset but limit jumps */
				bdi->smooth_level.calibration_offset = offset_change;
				bdi->smooth_level.calibration_set = true;

				/* Limit sudden jumps even during calibration */
				if (abs(offset_change) > 10) {
					/* Large calibration change - apply gradually */
					int step = (offset_change > 0) ? 5 : -5;
					bdi->smooth_level.target_level = bdi->smooth_level.current_level + step;
					printk(KERN_INFO "aw3215: large calibration change (%+d%%), applying gradually from %d%% to %d%%\n",
						   offset_change, bdi->smooth_level.current_level, bdi->smooth_level.target_level);
				} else {
					/* Small calibration change - apply immediately */
					bdi->smooth_level.current_level = battery_calibration_level;
					bdi->smooth_level.target_level = battery_calibration_level;
					printk(KERN_INFO "aw3215: battery calibrated to %d%% (offset=%d)\n",
						   battery_calibration_level, offset_change);
				}
			}

			//battery_calibration_level = -1; /* Reset parameter */
		}
	}

	time_diff_ms = jiffies_to_msecs(current_time - bdi->smooth_level.last_update_time);

	/* Handle calibration wait mode */
	if (bdi->smooth_level.calibration_wait_active) {
		int current_real_capacity = raw_capacity + bdi->smooth_level.calibration_offset;
		unsigned long wait_time_ms = jiffies_to_msecs(current_time - bdi->smooth_level.calibration_wait_start_time);

		if (is_charging) {
			/* Charging mode: wait for real capacity to catch up */
			if (current_real_capacity >= bdi->smooth_level.calibration_displayed_level) {
				/* Real capacity has caught up - exit wait mode */
				bdi->smooth_level.calibration_wait_active = false;
				bdi->smooth_level.target_level = current_real_capacity;

				printk(KERN_INFO "aw3215: charging wait completed - real capacity caught up to %d%%\n",
					   bdi->smooth_level.calibration_displayed_level);
			} else {
				/* Still waiting - stay at current level or increase very slowly */
				if (wait_time_ms < CALIBRATION_WAIT_TIME_MS) {
					/* Stay at current level */
					bdi->smooth_level.target_level = bdi->smooth_level.calibration_displayed_level;
					printk(KERN_DEBUG "aw3215: charging wait - staying at %d%% (real=%d%%, wait_time=%lums)\n",
						   bdi->smooth_level.calibration_displayed_level, current_real_capacity, wait_time_ms);
				} else {
					/* After wait time, allow very slow increase to avoid user confusion */
					bdi->smooth_level.target_level = min(bdi->smooth_level.calibration_displayed_level + 1, 100);
					printk(KERN_DEBUG "aw3215: charging wait timeout - allowing slow increase to %d%% (real=%d%%)\n",
						   bdi->smooth_level.target_level, current_real_capacity);
				}
			}
		} else {
			/* Discharging mode: handle based on calibration direction */
			if (bdi->smooth_level.calibration_real_level > bdi->smooth_level.calibration_displayed_level) {
				/* Calibration value higher - smooth descent to real value */
				bdi->smooth_level.target_level = bdi->smooth_level.calibration_real_level;

				/* Check if we've reached the target */
				if (bdi->smooth_level.current_level <= bdi->smooth_level.calibration_real_level) {
					bdi->smooth_level.calibration_wait_active = false;
					printk(KERN_INFO "aw3215: discharging descent completed - reached real capacity %d%%\n",
						   bdi->smooth_level.calibration_real_level);
				} else {
					printk(KERN_DEBUG "aw3215: discharging descent - moving from %d%% toward %d%%\n",
						   bdi->smooth_level.current_level, bdi->smooth_level.calibration_real_level);
				}
			} else {
				/* Calibration value lower - wait for natural discharge */
				if (current_real_capacity <= bdi->smooth_level.calibration_real_level) {
					/* Natural discharge has reached calibration value */
					bdi->smooth_level.calibration_wait_active = false;
					bdi->smooth_level.target_level = bdi->smooth_level.calibration_real_level;

					printk(KERN_INFO "aw3215: discharging wait completed - natural discharge reached %d%%\n",
						   bdi->smooth_level.calibration_real_level);
				} else {
					/* Keep current level, wait for natural discharge */
					bdi->smooth_level.target_level = bdi->smooth_level.current_level;
					printk(KERN_DEBUG "aw3215: discharging wait - staying at %d%%, waiting for natural discharge to %d%%\n",
						   bdi->smooth_level.current_level, bdi->smooth_level.calibration_real_level);
				}
			}
		}
	} else {
		/* Normal mode: update target level based on current voltage and apply calibration offset */

		if (bdi->smooth_level.calibration_set) {
			calculated_target = raw_capacity + bdi->smooth_level.calibration_offset;
		} else {
			calculated_target = raw_capacity;
		}

		/* Apply directional constraints to prevent jumps when charging state changes */
		if (is_charging) {
			/* During charging: only allow increases, and limit sudden jumps */
			if (calculated_target > bdi->smooth_level.current_level) {
				/* Limit sudden increases to prevent voltage boost jumps */
				int max_jump = 5; /* Maximum 5% jump at once */
				if (calculated_target - bdi->smooth_level.current_level > max_jump) {
					calculated_target = bdi->smooth_level.current_level + max_jump;
					printk(KERN_DEBUG "aw3215: limiting charging jump to %d%% (was %d%%)\n",
						   calculated_target, raw_capacity + bdi->smooth_level.calibration_offset);
				}
			}
		}

		bdi->smooth_level.target_level = calculated_target;
	}

	/* Apply directional constraints based on charging state */
	if (is_charging) {
		/* During charging: battery level can only increase or stay the same */
		if (calculated_target < bdi->smooth_level.current_level) {
			bdi->smooth_level.target_level = bdi->smooth_level.current_level; /* Don't decrease while charging */
		} else {
			bdi->smooth_level.target_level = calculated_target;
		}

		/* Special case: if already at 100% while charging, stay at 100% */
		if (bdi->smooth_level.current_level >= 100) {
			bdi->smooth_level.target_level = 100;
		}
	} else {
		/* During discharging: battery level can only decrease or stay the same */
		if (calculated_target > bdi->smooth_level.current_level) {
			bdi->smooth_level.target_level = bdi->smooth_level.current_level; /* Don't increase while discharging */
		} else {
			bdi->smooth_level.target_level = calculated_target;
		}

		/* Special case: if at 0% while discharging, stay at 0% */
		if (bdi->smooth_level.current_level <= 0) {
			bdi->smooth_level.target_level = 0;
		}
	}

	/* Check if charging state changed */
	if (bdi->smooth_level.charging_state != is_charging) {
		printk(KERN_DEBUG "aw3215: charging state changed from %s to %s at level %d%%\n",
			   bdi->smooth_level.charging_state ? "charging" : "discharging",
			   is_charging ? "charging" : "discharging",
			   bdi->smooth_level.current_level);

		bdi->smooth_level.charging_state = is_charging;

		/* When charging state changes, be very conservative with target level changes
		 * to prevent wrong-direction jumps */
		if (is_charging) {
			/* Just started charging - prevent voltage boost jumps */
			if (bdi->smooth_level.target_level < bdi->smooth_level.current_level) {
				bdi->smooth_level.target_level = bdi->smooth_level.current_level;
			} else {
				/* Limit sudden jumps when starting to charge */
				int max_initial_jump = 3; /* Maximum 3% jump when starting charge */
				if (bdi->smooth_level.target_level - bdi->smooth_level.current_level > max_initial_jump) {
					bdi->smooth_level.target_level = bdi->smooth_level.current_level + max_initial_jump;
					printk(KERN_INFO "aw3215: limiting initial charging jump to %d%% (from %d%% to %d%%)\n",
						   max_initial_jump, bdi->smooth_level.current_level, bdi->smooth_level.target_level);
				}
			}
		} else {
			/* Just started discharging - target can only be current level or lower */
			if (bdi->smooth_level.target_level > bdi->smooth_level.current_level) {
				bdi->smooth_level.target_level = bdi->smooth_level.current_level;
			}
		}
	}

	/* Determine update interval and max change based on mode */
	int update_interval_ms = BATTERY_LEVEL_CHANGE_INTERVAL_MS;
	int max_change = MAX_LEVEL_CHANGE_PER_INTERVAL;

	if (bdi->smooth_level.calibration_wait_active) {
		unsigned long wait_time_ms = jiffies_to_msecs(current_time - bdi->smooth_level.calibration_wait_start_time);
		if (wait_time_ms >= CALIBRATION_WAIT_TIME_MS) {
			/* After wait time, use very slow increase rate */
			update_interval_ms = BATTERY_LEVEL_CHANGE_INTERVAL_MS * CALIBRATION_SLOW_INCREASE_RATE;
		}
	}

	/* Only update if enough time has passed */
	if (time_diff_ms >= update_interval_ms) {
		if (bdi->smooth_level.current_level < bdi->smooth_level.target_level) {
			/* Battery level should increase */
			if (is_charging) {
				level_change = min(max_change,
								   bdi->smooth_level.target_level - bdi->smooth_level.current_level);
			} else {
				/* Discharging but target is higher - this shouldn't happen, fix target */
				bdi->smooth_level.target_level = bdi->smooth_level.current_level;
				level_change = 0;
				printk(KERN_DEBUG "aw3215: corrected target level during discharge (was higher than current)\n");
			}
		} else if (bdi->smooth_level.current_level > bdi->smooth_level.target_level) {
			/* Battery level should decrease - only allow during discharging */
			if (!is_charging) {
				level_change = -min(max_change,
									bdi->smooth_level.current_level - bdi->smooth_level.target_level);
			} else {
				/* Charging but target is lower - keep current level (wait mode) */
				bdi->smooth_level.target_level = bdi->smooth_level.current_level;
				level_change = 0;
				printk(KERN_DEBUG "aw3215: maintaining level during charging wait mode\n");
			}
		}

		if (level_change != 0) {
			int new_level = bdi->smooth_level.current_level + level_change;

			/* Final safety check - ensure direction is correct */
			if ((is_charging && new_level >= bdi->smooth_level.current_level) ||
				(!is_charging && new_level <= bdi->smooth_level.current_level)) {
				bdi->smooth_level.current_level = new_level;
				bdi->smooth_level.last_update_time = current_time;
			} else {
				printk(KERN_DEBUG "aw3215: blocked wrong-direction level change: %s, %d->%d\n",
					   is_charging ? "charging" : "discharging",
					   bdi->smooth_level.current_level, new_level);
			}
		}
	}

	/* Ensure level stays within bounds */
	if (bdi->smooth_level.current_level < 0) {
		bdi->smooth_level.current_level = 0;
	} else if (bdi->smooth_level.current_level > 100) {
		bdi->smooth_level.current_level = 100;
	}

	/* Also ensure target level stays within bounds */
	if (bdi->smooth_level.target_level < 0) {
		bdi->smooth_level.target_level = 0;
	} else if (bdi->smooth_level.target_level > 100) {
		bdi->smooth_level.target_level = 100;
	}

	result_level = bdi->smooth_level.current_level;

	/* Check if calibration is needed during charging */
	if (aw3215_should_calibrate(bdi, result_level) && battery_calibration_enabled) {
		bdi->smooth_level.last_calibration_time = current_time;
		bdi->smooth_level.last_calibration_level = result_level;

		printk(KERN_DEBUG "aw3215: scheduling calibration at level %d%%\n", result_level);

		/* Schedule calibration work with small delay to ensure stable state */
		schedule_delayed_work(&bdi->smooth_level.calibration_work, msecs_to_jiffies(100));
	}

	/* Debug information */
	if (level_change != 0) {
		printk(KERN_DEBUG "aw3215: voltage=%dmV, raw=%d%%, current=%d%%, target=%d%%, change=%+d, %s, cal=%s\n",
			   voltage_mv, raw_capacity, bdi->smooth_level.current_level,
			   bdi->smooth_level.target_level, level_change,
			   is_charging ? "CHG" : "DIS",
			   bdi->smooth_level.calibration_set ? "Y" : "N");
	}

	mutex_unlock(&bdi->smooth_level.level_mutex);

	return result_level;
}
#endif

static int aw3215_battery_get_health(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
	val->intval = POWER_SUPPLY_HEALTH_GOOD;

	return 0;
}

static int aw3215_battery_get_online(struct aw3215_dev_info *bdi,union power_supply_propval *val)
{
	val->intval = 1;/*bat on*/

	return 0;
}

static int aw3215_battery_set_online(struct aw3215_dev_info *bdi,const union power_supply_propval *val)
{
	return 0;
}


static int aw3215_battery_get_property(struct power_supply *psy,enum power_supply_property psp,
		union power_supply_propval *val)
{
	struct aw3215_dev_info *bdi =
			container_of(psy, struct aw3215_dev_info, battery);
	int ret;

	//dev_dbg(bdi->dev, "prop: %d\n", psp);

	//pm_runtime_get_sync(bdi->dev);

	switch (psp) {

	case POWER_SUPPLY_PROP_HEALTH:
		ret = aw3215_battery_get_health(bdi, val);
		break;
	case POWER_SUPPLY_PROP_ONLINE:
		ret = aw3215_battery_get_online(bdi, val);
		break;

	case POWER_SUPPLY_PROP_TEMP:
		val->intval = get_adc2_voltage();
		ret = 0;
		break;

	case POWER_SUPPLY_PROP_VOLTAGE_NOW:
		//#ifdef CONFIG_ZX234290_ADC
		val->intval = get_adc1_voltage() - 15;
		ret = 0;
		break;

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	case POWER_SUPPLY_PROP_CAPACITY:
		val->intval = aw3215_update_smooth_level(bdi);
		printk(KERN_DEBUG "aw3215: battery capacity = %d%% (10000mAh)\n", val->intval);
		ret = 0;
		break;
#endif

	default:
		ret = -ENODATA;
	}

	//pm_runtime_put_sync(bdi->dev);
	return ret;
}

static int aw3215_battery_set_property(struct power_supply *psy,enum power_supply_property psp,
		const union power_supply_propval *val)
{
	struct aw3215_dev_info *bdi =
			container_of(psy, struct aw3215_dev_info, battery);
	int ret;

	//dev_dbg(bdi->dev, "prop: %d\n", psp);

	//pm_runtime_put_sync(bdi->dev);

	switch (psp) {
	case POWER_SUPPLY_PROP_ONLINE:
		ret = aw3215_battery_set_online(bdi, val);
		break;
	default:
		ret = -EINVAL;
	}

	//pm_runtime_put_sync(bdi->dev);
	return ret;
}

static int aw3215_battery_property_is_writeable(struct power_supply *psy,enum power_supply_property psp)
{
	int ret;

	switch (psp) {
	case POWER_SUPPLY_PROP_ONLINE:
		ret = 1;
		break;
	default:
		ret = 0;
	}

	return ret;
}

static enum power_supply_property aw3215_battery_properties[] = {
	POWER_SUPPLY_PROP_HEALTH,
	POWER_SUPPLY_PROP_ONLINE,
	POWER_SUPPLY_PROP_TEMP,
	POWER_SUPPLY_PROP_VOLTAGE_NOW,
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	POWER_SUPPLY_PROP_CAPACITY,
#endif
};

static void aw3215_battery_init(struct power_supply *battery)
{
	battery->name = "battery";
	battery->type = POWER_SUPPLY_PCAC_UNKNOWN;
	battery->properties = aw3215_battery_properties;
	battery->num_properties = ARRAY_SIZE(aw3215_battery_properties);
	battery->get_property = aw3215_battery_get_property;
	battery->set_property = aw3215_battery_set_property;
	battery->property_is_writeable = aw3215_battery_property_is_writeable;
}

#if 1
static irqreturn_t aw3215_charger_in_irq_primary_handler(int irq, struct aw3215_dev_info * bdi)
{
	s_chg_chin_cnt++;
	disable_irq_nosync(irq);
	//pcu_int_clear(irq);
	pcu_clr_irq_pending(irq);
	up(&bdi->chgstate_sem);
	//up(&bdi->chgin_sem);
	return IRQ_HANDLED;
}
static irqreturn_t aw3215_charger_state_irq_primary_handler(int irq, struct aw3215_dev_info * bdi)
{
	s_chg_chstate_cnt++;
	disable_irq_nosync(irq);
	//pcu_int_clear(irq);
	pcu_clr_irq_pending(irq);
	up(&bdi->chgstate_sem);
	
	return IRQ_HANDLED;
}

#endif
#if 1
static irqreturn_t aw3215_chg_irq_handler_thread(void *data)
{
	struct aw3215_dev_info *bdi = data;
	struct aw3215_platform_data *pdata = bdi->pdata;
	//unsigned int state_rcd = bdi->chg_state;
	int g_gpio_state=CHG_STOP;
	int g_gpio_in =USB_IN;
	bool chg_changed_flag = false;
#if defined(CONFIG_JCV_HW_POWERBANK_DZ801) || defined(CONFIG_JCV_HW_GS28V_V1)
	int voltage_now;
#endif
	
	struct sched_param param = { .sched_priority = 2 };
	param.sched_priority= 31;
	sched_setscheduler(current, SCHED_FIFO, &param);

	while(1)
	{
		down(&bdi->chgstate_sem);
		g_gpio_in = gpio_get_value(pdata->gpio_chgin);		
		g_gpio_state = gpio_get_value(pdata->gpio_chgstate);
		if(bdi->chgin_type!=g_gpio_in){			

			bdi->chgin_type = g_gpio_in;
			if(g_gpio_in == USB_IN){
				irq_set_irq_type(bdi->chgin_irq, IRQ_TYPE_LEVEL_HIGH);				
				printk("chg usb in\n");
				dwc_otg_chg_inform(0);/*usb in*/
		#if defined(CONFIG_JCV_HW_POWERBANK_DZ801) || defined(CONFIG_JCV_HW_GS28V_V1)
				voltage_now = get_adc1_voltage() - 15;
				printk("%s: salvikie voltage_now %d\n", __func__, voltage_now);
				if(bdi->stopchg_flag == CHG_STOP_REASON_TEMP) {
					bdi->chg_state = POWER_SUPPLY_STATUS_NOT_CHARGING;				
					printk("chg temp abnormal\n");
				}
			#if 0
				else if(voltage_now >= 4180) {
					bdi->chg_state = POWER_SUPPLY_STATUS_FULL;				
					printk("chg full\n");
				}
			#endif
				else {
					bdi->chg_state = POWER_SUPPLY_STATUS_CHARGING;				
					printk("chg charging\n");
				}
		#else
				if(CHG_START == g_gpio_state){
					bdi->chg_state = POWER_SUPPLY_STATUS_CHARGING;				
					printk("chg charging\n");
				}
				else if(bdi->stopchg_flag == CHG_STOP_REASON_TEMP){
					bdi->chg_state = POWER_SUPPLY_STATUS_NOT_CHARGING;				
					printk("chg temp abnormal\n");
				}
				else{
					bdi->chg_state = POWER_SUPPLY_STATUS_FULL;				
					printk("chg full\n");
				}
		#endif
			}
			else{/*usb out*/
				bdi->chg_state = POWER_SUPPLY_STATUS_DISCHARGING;				
				printk("chg usb out\n");
				irq_set_irq_type(bdi->chgin_irq, IRQ_TYPE_LEVEL_LOW);		
				dwc_otg_chg_inform(1);/*usb out*/
				
			}
			chg_changed_flag = true;	
			s_chg_chin_cnt--;
			enable_irq(bdi->chgin_irq);
		}
		else if(bdi->chgstate_type!=g_gpio_state){
			
			bdi->chgstate_type=g_gpio_state;
		#if defined(CONFIG_JCV_HW_POWERBANK_DZ801) || defined(CONFIG_JCV_HW_GS28V_V1)
			if (CHG_START == g_gpio_state){			
				printk("chg out det low \n");
				irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);	

				// if( bdi->chgin_type == USB_IN){		
				// 	printk("chg state is on need disable chg out\n");
				// 	// gpio 
				// }
				// else {
				// 	printk("chg state dischargeing enable chg out\n");			
				// 	bdi->chg_state=POWER_SUPPLY_STATUS_DISCHARGING;
				// 	chg_changed_flag = true;	
				// }
			}
			else {
				printk("chg out det high \n");
				irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_LOW);

				// if( bdi->chgin_type == USB_IN){		
				// 	printk("chg state is on\n");
				// 	bdi->chg_state=POWER_SUPPLY_STATUS_CHARGING;
				// 	irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);		
				// 	chg_changed_flag = true;
				// }
				// else {
				// 	printk("chg state dischargeing\n");			
				// 	bdi->chg_state=POWER_SUPPLY_STATUS_DISCHARGING;
				// 	chg_changed_flag = true;	
				// }
			}
		#else
			if (CHG_START == g_gpio_state){			
				printk("chg state charging\n");
				bdi->chg_state=POWER_SUPPLY_STATUS_CHARGING;
				irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);		
				chg_changed_flag = true;
			}
			else {
				if( bdi->chgin_type == USB_IN){		
					printk("chg state maybe full\n");
					bdi->chg_state=POWER_SUPPLY_STATUS_FULL;			
					wake_lock(&(bdi->wlock_chgfull)); 
					mod_timer(&bdi->changed_timer, jiffies + msecs_to_jiffies(500));			
				}
				else{
					printk("chg state dischargeing\n");				
					bdi->chg_state=POWER_SUPPLY_STATUS_DISCHARGING;
					chg_changed_flag = true;				
				}
				irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_LOW);		
			}
		#endif
			
			s_chg_chstate_cnt--;
			enable_irq(bdi->chgstate_irq);
		}	
		else{
			printk("chg int maybe handled,in=%d,state = %d\n",s_chg_chin_cnt,s_chg_chstate_cnt);
			if(s_chg_chstate_cnt){
				s_chg_chstate_cnt--;
				enable_irq(bdi->chgstate_irq);
			}
			if(s_chg_chin_cnt){	
				s_chg_chin_cnt--;
				enable_irq(bdi->chgin_irq);	
			}
		}
		if(true == chg_changed_flag){
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
			aw3215_power_supply_changed_safe(bdi);
#else
			power_supply_changed(&bdi->charger);
			power_supply_changed(&bdi->battery);
#endif
			chg_changed_flag = false;
		}
	}
	
	return 0;
}
#endif
#if 0
static irqreturn_t aw3215_chgin_irq_handler_thread(void *data)
{
	struct aw3215_dev_info *bdi = data;
	struct aw3215_platform_data *pdata = bdi->pdata;
	int g_gpio_in =0;
	struct sched_param param = { .sched_priority = 2 };

	param.sched_priority= 31;
	sched_setscheduler(current, SCHED_FIFO, &param);

	while(1)
	{
		down(&bdi->chgin_sem);

		g_gpio_in = gpio_get_value(pdata->gpio_chgin);

		/*charging status*/
		if (g_gpio_in == USB_IN) {
			if (bdi->chgin_type == USB_IN) {
				printk(KERN_INFO"chg usb in err\n");
			} 
			else if (bdi->stopchg_flag == CHG_STOP_REASON_TEMP){
				bdi->chgin_type = USB_IN;
				printk(KERN_INFO"chg usb in temp err\n");
				bdi->chg_state = POWER_SUPPLY_STATUS_NOT_CHARGING;
				dwc_otg_chg_inform(0);/*usb in*/
			}
			else{
				bdi->chgin_type = USB_IN;

				disable_irq(bdi->chgstate_irq);
				//gpio_set_value(pdata->gpio_chgen, CHG_EN_TYPE);
				//bdi->chg_en = true;
				//bdi->chgstate_type = CHG_START;

				/* start charging in 5.3ms after enable */
				if (gpio_get_value(pdata->gpio_chgstate))
					mdelay(7);
				if (bdi->chgstate_type == gpio_get_value(pdata->gpio_chgstate))
					printk(KERN_INFO "chg still not chargin"); /* should not go here */
				bdi->chgstate_type = gpio_get_value(pdata->gpio_chgstate);
				bdi->chg_state = POWER_SUPPLY_STATUS_CHARGING;

				irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);

				enable_irq(bdi->chgstate_irq);

				printk(KERN_INFO"chg usb in\n");
				dwc_otg_chg_inform(0);/*usb in*/

				//irq_set_irq_type(bdi->chgin_irq, IRQ_TYPE_LEVEL_HIGH);		
			}

			irq_set_irq_type(bdi->chgin_irq, IRQ_TYPE_LEVEL_HIGH);
		} else {
			bdi->chg_state = POWER_SUPPLY_STATUS_DISCHARGING;
			if (bdi->chgin_type == USB_OUT) {
				printk(KERN_INFO"chg usb out err\n");
			} else {
				bdi->chgin_type = USB_OUT;
				//SINT32 Usb_plug = DISCONNECTED_FROM_HOST;
				printk(KERN_INFO"chg usb out\n");

				dwc_otg_chg_inform(1);/*usb out*/

				//irq_set_irq_type(bdi->chgin_irq, IRQ_TYPE_LEVEL_LOW);
			}
			irq_set_irq_type(bdi->chgin_irq, IRQ_TYPE_LEVEL_LOW);
		}

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
		aw3215_power_supply_changed_safe(bdi);
#else
		power_supply_changed(&bdi->charger);
		power_supply_changed(&bdi->battery);
#endif
		enable_irq(bdi->chgin_irq);

	}
	return 0;
}

static irqreturn_t aw3215_chgstate_irq_handler_thread(void *data)
{
	struct aw3215_dev_info *bdi = data;
	struct aw3215_platform_data *pdata = bdi->pdata;
	//unsigned int state_rcd = bdi->chg_state;
	int g_gpio_state=CHG_STOP;
	uint adc1_v= 0;
	struct sched_param param = { .sched_priority = 2 };

	param.sched_priority= 31;
	sched_setscheduler(current, SCHED_FIFO, &param);

	while(1)
	{
		down(&bdi->chgstate_sem);

		g_gpio_state = gpio_get_value(pdata->gpio_chgstate);

		/*charging status*/
		if (g_gpio_state == CHG_START) {	/*low charging*/
			bdi->chg_state = POWER_SUPPLY_STATUS_CHARGING;

			if (bdi->chgstate_type == CHG_START) {
				printk(KERN_INFO"chg chging err!\n");
			} else {
				bdi->chgstate_type = CHG_START;

				//irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
					aw3215_power_supply_changed_safe(bdi);
#else
					power_supply_changed(&bdi->charger);
					power_supply_changed(&bdi->battery);
#endif
				printk(KERN_INFO"chg charging\n");
			}
			irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_HIGH);
		} else {/*high stop charging*/
			if (bdi->chgstate_type == CHG_STOP) {
				printk(KERN_INFO"chg full err!\n");
			} else {
				if ((bdi->chgin_type == USB_IN)&&(bdi->stopchg_flag == CHG_STOP_REASON_TEMP)){
					bdi->chg_state = POWER_SUPPLY_STATUS_NOT_CHARGING;
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
					aw3215_power_supply_changed_safe(bdi);
#else
					power_supply_changed(&bdi->charger);
					power_supply_changed(&bdi->battery);
#endif
				}
				else if(bdi->chgin_type == USB_IN){
					bdi->chg_state = POWER_SUPPLY_STATUS_FULL;
					wake_lock(&(bdi->wlock_chgfull)); 
					mod_timer(&bdi->changed_timer, jiffies + msecs_to_jiffies(500));			
				}
				else{
					bdi->chg_state = POWER_SUPPLY_STATUS_DISCHARGING;
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
						aw3215_power_supply_changed_safe(bdi);
#else
						power_supply_changed(&bdi->charger);
						power_supply_changed(&bdi->battery);
#endif
					}
					
				bdi->chgstate_type = CHG_STOP;

				printk(KERN_INFO "chg %s %s stop\n",
				(bdi->chg_state == POWER_SUPPLY_STATUS_FULL) ? "full" : " ",
				(bdi->chg_state == POWER_SUPPLY_STATUS_NOT_CHARGING) ? "temp error":"discharging");

			}
			irq_set_irq_type(bdi->chgstate_irq, IRQ_TYPE_LEVEL_LOW);
		}
		enable_irq(bdi->chgstate_irq);

	}
	return 0;
}

#endif
static int aw3215_setup_pdata(struct aw3215_dev_info *bdi,
		struct aw3215_platform_data *pdata)
{
	int ret;


	if (!gpio_is_valid(pdata->gpio_chgen))
		return -1;

	ret = gpio_request(pdata->gpio_chgen, "chg_en");
	if (ret < 0)
		goto out;
	ret = zx29_gpio_config(pdata->gpio_chgen, pdata->gpio_chgen_gpio_sel);
	ret = gpio_direction_output(pdata->gpio_chgen, CHG_EN_TYPE);
	bdi->chg_en =true;
	bdi->stopchg_flag=CHG_STOP_DEFALT;
#ifndef CONFIG_AIC8800_MIFI_EN
    //chg termination current ctrl
	if (!gpio_is_valid(pdata->gpio_chgctrl))
		return -1;

	ret = gpio_request(pdata->gpio_chgctrl, "chg_ctrl");
	if (ret < 0)
		goto out;
	ret = zx29_gpio_config(pdata->gpio_chgctrl, pdata->gpio_chgctrl_gpio_sel);
	if (ret < 0)
		goto out;
	ret = gpio_direction_output(pdata->gpio_chgctrl, 0);
	if (ret < 0)
		goto out;
#endif
	return 0;
out:
	//gpio_free(pdata->gpio_int);
	return -1;
}


static int aw3215_init_state(struct aw3215_dev_info *bdi)
{
	struct aw3215_platform_data *pdata;
	int ret = 0;
	unsigned int g_gpio_in,g_gpio_state;
	unsigned int chgin_irq_type=IRQ_TYPE_NONE;
	unsigned int chgstate_irq_type=IRQ_TYPE_NONE;

	pdata = bdi->pdata;

	if (!gpio_is_valid(pdata->gpio_chgin))
		goto error;

	ret = gpio_request(pdata->gpio_chgin, "chg_usbin");
	if (ret < 0)
		goto error;

	zx29_gpio_pd_pu_set(pdata->gpio_chgin, IO_CFG_PULL_DISABLE);
	ret = zx29_gpio_config(pdata->gpio_chgin, pdata->gpio_chgin_gpio_sel);
	if (ret < 0)
		goto error;

	ret = gpio_direction_input(pdata->gpio_chgin);
	if (ret < 0)
		goto error;

	mdelay(20);/*?*/
	g_gpio_in = gpio_get_value(pdata->gpio_chgin);
	printk(KERN_INFO"%s: salvikie gpio%d g_gpio_in=%d\n", __func__, pdata->gpio_chgin, g_gpio_in);

	if ( USB_IN == g_gpio_in){
		bdi->chgin_type = USB_IN;
		printk(KERN_INFO"init usb in\n");
		chgin_irq_type = IRQ_TYPE_LEVEL_HIGH;
#ifdef _CHARGER_UNNOTIFY_USB_
		if(get_adc1_voltage()>USB_ENUM_MIN)
#endif
		dwc_otg_chg_inform(0);/*usb in*/
	}
	else {
		bdi->chgin_type = USB_OUT;
		bdi->chg_state=POWER_SUPPLY_STATUS_DISCHARGING;
		printk(KERN_INFO"init usb out\n");
		chgin_irq_type = IRQ_TYPE_LEVEL_LOW;
		dwc_otg_chg_inform(1);/*usb out*/
	}

	bdi->chgin_irq= gpio_to_irq(pdata->gpio_chgin);

	ret = zx29_gpio_config(pdata->gpio_chgin,pdata->gpio_chgin_fun_sel);
	if (ret < 0)
		goto error;
	//zx29_gpio_set_inttype(pdata->gpio_chgin,chgin_irq_type);  //INT_POSEDGE
	//pcu_clr_irq_pending(bdi->chgin_irq);
	irq_set_irq_type(bdi->chgin_irq, chgin_irq_type);

    if (!gpio_is_valid(pdata->gpio_chgstate))
		goto error;

	ret = gpio_request(pdata->gpio_chgstate, "chg_state");
	if (ret < 0)
		goto error;

	zx29_gpio_pd_pu_set(pdata->gpio_chgstate, IO_CFG_PULL_DISABLE);

	bdi->chgstate_irq= gpio_to_irq(pdata->gpio_chgstate);

	ret = zx29_gpio_config(pdata->gpio_chgstate, pdata->gpio_chgstate_gpio_sel);
	if (ret < 0)
		goto error;

	ret = gpio_direction_input(pdata->gpio_chgstate);
	if (ret < 0)
		goto error;

	mdelay(20);/*?*/
	g_gpio_state = gpio_get_value(pdata->gpio_chgstate);
	printk(KERN_INFO"%s: salvikie gpio%d g_gpio_state=%d\n", __func__, pdata->gpio_chgstate, g_gpio_state);

#if !defined(CONFIG_JCV_HW_POWERBANK_DZ801) && !defined(CONFIG_JCV_HW_GS28V_V1)
//#if 1
	if (CHG_START == g_gpio_state){
		bdi->chgstate_type=CHG_START ;
		bdi->chg_state=POWER_SUPPLY_STATUS_CHARGING;

		printk(KERN_INFO"init chg state chargeing\n");
		chgstate_irq_type = IRQ_TYPE_LEVEL_HIGH;
		//dwc_otg_chg_inform(0);/*usb in*/
	}
	else {
		bdi->chgstate_type =CHG_STOP ;

		printk(KERN_INFO"init chg state discharger or full\n");
		chgstate_irq_type = IRQ_TYPE_LEVEL_LOW;
		//dwc_otg_chg_inform(1);/*usb out*/
		if( bdi->chgin_type == USB_IN)
			bdi->chg_state=POWER_SUPPLY_STATUS_FULL;
		else
			bdi->chg_state=POWER_SUPPLY_STATUS_DISCHARGING;
	}
#else
	if (CHG_START == g_gpio_state) {
		chgstate_irq_type = IRQ_TYPE_LEVEL_HIGH;
	}
	else {
		chgstate_irq_type = IRQ_TYPE_LEVEL_LOW;
	}

	if( bdi->chgin_type == USB_IN) {
		bdi->chgstate_type=CHG_START ;
		bdi->chg_state=POWER_SUPPLY_STATUS_CHARGING;

		printk(KERN_INFO"salvikie usbin init chg state chargeing\n");
		
		//dwc_otg_chg_inform(0);/*usb in*/
	}
	else {
		bdi->chgstate_type =CHG_STOP ;

		printk(KERN_INFO"salvikie usbout init chg state discharger or full\n");
		bdi->chg_state=POWER_SUPPLY_STATUS_DISCHARGING;
	}

#endif

	ret = zx29_gpio_config(pdata->gpio_chgstate,pdata->gpio_chgstate_fun_sel);
	if (ret < 0)
		goto error;
	
	//zx29_gpio_set_inttype(pdata->gpio_chgstate,chgstate_irq_type);  //INT_POSEDGE

	//pcu_clr_irq_pending(bdi->chgstate_irq);
	irq_set_irq_type(bdi->chgstate_irq, chgstate_irq_type);

	return 0;
error:
	printk(KERN_INFO"chg gpio  error ret = %d\n",ret);
	return -1;
}



static void aw3215_charge_typedet(T_TYPE_USB_DETECT chg_type)
{
	//u8 ret;
	#ifdef DBG_CHARGE
	//printk(KERN_INFO"charge type is %d in\n",chg_type);
	#endif

	if(TYPE_ADAPTER == chg_type){
		printk(KERN_INFO"chg type DC\n");
		g_bdi->charger.type = POWER_SUPPLY_PCAC__AC;
	}
	else{
		printk(KERN_INFO"chg type PC\n");
		g_bdi->charger.type = POWER_SUPPLY_PCAC__PC;
	}

}


#if defined(CONFIG_DEBUG_FS)
static ssize_t debugfs_regs_write(struct file *file, const char __user *buf,size_t nbytes, loff_t *ppos)
{
	unsigned int val1, val2;
	//u8 reg, value;
	int ret = 0;
	char *kern_buf;
	//struct seq_file	*s = file->private_data;
	//struct aw3215_dev_info *aw3215 = s->private;

	kern_buf = kzalloc(nbytes, GFP_KERNEL);

	if (!kern_buf) {
		printk(KERN_INFO "aw3215_charger: Failed to allocate buffer\n");
		return -ENOMEM;
	}

	if (copy_from_user(kern_buf, (void  __user *)buf, nbytes)) {
		kfree(kern_buf);
		return -ENOMEM;
	}
	printk(KERN_INFO "%s input str=%s,nbytes=%d \n", __func__, kern_buf,nbytes);

	ret = sscanf(kern_buf, "%x:%x", &val1, &val2);
	if (ret < 2) {
		printk(KERN_INFO "sgm40561_charger: failed to read user buf, ret=%d, input 0x%x:0x%x\n",
				ret,val1, val2);
		kfree(kern_buf);
		return -EINVAL;
	}
	kfree(kern_buf);

	return ret ? ret : nbytes;
}

static int debugfs_regs_show(struct seq_file *s, void *v)
{
	//int i;
	int ret=0;
	//int curr = 0;
	struct aw3215_dev_info *aw3215 = s->private;

	/*charger type*/
	if((int)aw3215->charger.type == POWER_SUPPLY_PCAC__PC){
		seq_printf(s, "charger type is  PC\n");
	}
	else if((int)aw3215->charger.type == POWER_SUPPLY_PCAC__AC){
		seq_printf(s, "charger type is  AC\n");
	}
	else
	seq_printf(s, "charger type is  unknow = %d\n",aw3215->charger.type);

	seq_printf(s, "mmi charger config state = %d\n",aw3215->chg_en);
	seq_printf(s, "chg in state = %s\n",(aw3215->chgin_type==USB_IN)? "USB_IN": "USB_OUT");
	seq_printf(s, "chg_state  state = %s\n",aw3215->chgstate_type ? "CHG_STOP": "CHG_START");
#ifdef CHG_DEBUG
	seq_printf("chgstate int en reg1 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_ENABLE_SET+0x4));
	seq_printf("chgstate int en reg2 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_ENABLE_SET+0x8));
	seq_printf("chgstate int pending reg1 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_PENDING_SET+0x4));
	seq_printf("chgstate int pending reg2 =0x%x:\n", zx_read_reg(base_testtt + GIC_DIST_PENDING_SET+0x8));
#endif
	return ret;
}

#define DEBUGFS_FILE_ENTRY(name) \
static int debugfs_##name##_open(struct inode *inode, struct file *file) \
{\
return single_open(file, debugfs_##name##_show, inode->i_private); \
}\
\
static const struct file_operations debugfs_##name##_fops = { \
.owner= THIS_MODULE, \
.open= debugfs_##name##_open, \
.write=debugfs_##name##_write, \
.read= seq_read, \
.llseek= seq_lseek, \
.release= single_release, \
}

DEBUGFS_FILE_ENTRY(regs);

static struct dentry *g_charger_root;

static void debugfs_charger_init(struct aw3215_dev_info  *aw3215)
{
	struct dentry *root;
	struct dentry *node;
	//int i;

	if(!aw3215)
		return;

	//create root
	root = debugfs_create_dir("charger_zx29", NULL);
	if (!root)	{
		dev_err(aw3215->dev, "debugfs_create_dir err=%ld\n", IS_ERR(root));
		goto err;
	}

	//print regs;
	node = debugfs_create_file("regs", S_IRUGO | S_IWUGO, root, aw3215,  &debugfs_regs_fops);
	if (!node){
		dev_err(aw3215->dev, "debugfs_create_dir err=%ld\n", IS_ERR(node));
		goto err;
	}

	g_charger_root = (void *)root;
	return;
err:
	dev_err(aw3215->dev, "debugfs_charger_init err\n");
}

#endif

static void aw3215_changed_timer_function(unsigned long data)
{
	struct aw3215_dev_info *bdi = (void *)data;
#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	aw3215_power_supply_changed_safe(bdi);
#else
	power_supply_changed(&bdi->charger);
	power_supply_changed(&bdi->battery);
#endif
	//printk("chg timer callback\n");
	del_timer(&bdi->changed_timer);
	wake_unlock(&(bdi->wlock_chgfull));
	printk("chg timer callback end\n");

}

static int __devinit aw3215_charger_probe(struct platform_device *pdev)
{
	struct aw3215_platform_data *pdata = pdev->dev.platform_data;
	struct device *dev = &pdev->dev;
	struct aw3215_dev_info *bdi;
	//unsigned long flag;
	int ret;

	bdi = devm_kzalloc(dev, sizeof(*bdi), GFP_KERNEL);
	if (!bdi) {
		dev_err(dev, "Can't alloc bdi struct\n");
		return -ENOMEM;
	}
	bdi->dev = dev;
	bdi->pdata = pdata;

	//printk(KERN_INFO "charger probe.\n");

	bdi->chg_state = POWER_SUPPLY_STATUS_UNKNOWN;
	bdi->charger.type = POWER_SUPPLY_TYPE_UNKNOWN;

	g_bdi = bdi;

	ret = aw3215_setup_pdata(bdi, pdata);
	if (ret) {
		dev_err(dev, "Can't get irq info\n");
		return -EINVAL;
	}

	aw3215_charger_init(&bdi->charger);

	ret = power_supply_register(dev, &bdi->charger);
	if (ret) {
		dev_err(dev, "Can't register charger\n");
		goto out2;
	}
	//printk(KERN_INFO "aw3215_probe power_supply_register charger ok.\n");

	aw3215_battery_init(&bdi->battery);

	ret = power_supply_register(dev, &bdi->battery);
	if (ret) {
		dev_err(dev, "Can't register battery\n");
		goto out1;
	}
	//printk(KERN_INFO "aw3215_probe power_supply_register battery ok.\n");
	//sema_init(&bdi->chgin_sem, 0);
	sema_init(&bdi->chgstate_sem, 0);

	dwc_chg_Regcallback(aw3215_charge_typedet);/*register for usb*/
	aw3215_init_state(bdi);
	init_timer(&bdi->changed_timer);
	bdi->changed_timer.function = aw3215_changed_timer_function;
	bdi->changed_timer.data = (unsigned long)bdi;
	wake_lock_init(&(bdi->wlock_chgfull), WAKE_LOCK_SUSPEND, "aw3215_wake_lock_chgfull");

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	/* Initialize smooth battery level control */
	aw3215_init_smooth_level(bdi);
#endif

/*chg in*/
	ret = request_irq(bdi->chgin_irq, aw3215_charger_in_irq_primary_handler,IRQF_NO_THREAD, "aw3215-chgin", bdi);
	if (ret < 0) {
		dev_err(dev, "Can't set up irq handler\n");
		if (bdi->pdata->gpio_chgin)
			gpio_free(bdi->pdata->gpio_chgin);

		goto out3;
	}
	irq_set_irq_wake(bdi->chgin_irq, 1);
	//bdi->chgin_irq_thread = kthread_run(aw3215_chgin_irq_handler_thread, bdi, "aw3215-chgin");
	//BUG_ON(IS_ERR(bdi->chgin_irq_thread));

/*chg state*/
	ret = request_irq(bdi->chgstate_irq, aw3215_charger_state_irq_primary_handler,IRQF_NO_THREAD, "aw3215-chgstate", bdi);
	if (ret < 0) {
		dev_err(dev, "Can't set up irq handler\n");
		if (bdi->pdata->gpio_chgstate)
			gpio_free(bdi->pdata->gpio_chgstate);

		goto out3;
	}
	irq_set_irq_wake(bdi->chgstate_irq, 1);
	//bdi->chgstate_irq_thread = kthread_run(aw3215_chgstate_irq_handler_thread, bdi, "aw3215-chgstate");
	//BUG_ON(IS_ERR(bdi->chgstate_irq_thread));
	bdi->chgstate_irq_thread = kthread_run(aw3215_chg_irq_handler_thread, bdi, "aw3215-chgstate");
	BUG_ON(IS_ERR(bdi->chgstate_irq_thread));

#if defined(CONFIG_DEBUG_FS)
	debugfs_charger_init(bdi);
#endif

#ifdef DBG_CHARGE
	//printk(KERN_INFO "aw3215_probe end.\n");
#endif

	return 0;

out1:
	power_supply_unregister(&bdi->battery);
out2:
	//pm_runtime_disable(dev);
	power_supply_unregister(&bdi->charger);
out3:

	return ret;

}

static int aw3215_charger_remove(struct platform_device *pdev)
{
	struct aw3215_platform_data *pdata = pdev->dev.platform_data;

	power_supply_unregister(&(g_bdi->battery));
	power_supply_unregister(&(g_bdi->charger));

#ifdef CONFIG_CHARLIEPLEX_LED_DIGITS
	/* Cancel any pending calibration work */
	cancel_delayed_work_sync(&g_bdi->smooth_level.calibration_work);
	cancel_delayed_work_sync(&g_bdi->smooth_level.restore_charging_work);

	/* Cleanup smooth level mutex */
	mutex_destroy(&g_bdi->smooth_level.level_mutex);
#endif

	pm_runtime_disable(g_bdi->dev);

	if (pdata->gpio_chgctrl)
		gpio_free(pdata->gpio_chgctrl);
	if (pdata->gpio_chgen)
		gpio_free(pdata->gpio_chgen);
	if (pdata->gpio_chgin)
		gpio_free(pdata->gpio_chgin);
	if (pdata->gpio_chgstate)
		gpio_free(pdata->gpio_chgstate);


#if defined(CONFIG_DEBUG_FS)
	if(g_charger_root){
		//printk(KERN_INFO "aw3215_device_exit:debugfs_remove_recursive \n");
		debugfs_remove_recursive(g_charger_root);
	}
#endif

	return 0;
}


static struct platform_driver zx29_charger_driver = {
	.probe		= aw3215_charger_probe,
	.remove		= __devexit_p(aw3215_charger_remove),
	.driver		= {
		.name	= "aw3215-charger",
		.owner	= THIS_MODULE,
	},
};

//module_platform_driver(zx29_charger_driver);


static int __init zx29_charger_init(void)
{
	return platform_driver_register(&zx29_charger_driver);
}

static void __exit zx29_charger_exit(void)
{
	platform_driver_unregister(&zx29_charger_driver);
}

module_init(zx29_charger_init);
module_exit(zx29_charger_exit);


MODULE_AUTHOR("Mark A. Greer <<EMAIL>>");
MODULE_DESCRIPTION("AW3215 Charger Driver");
MODULE_LICENSE("GPL");
MODULE_ALIAS("platform:zx29_charger");

