/*******************************************************************************
 * Copyright (C) 2016, ZXIC Corporation.
 *
 * File Name: gpio_cfg.h
 * File Mark:
 * Description:define gpio config for EVB

 * Author:        ZXIC
 * Date:          2016-07-19
 *
 * History 1:
  ********************************************************************************/

#ifndef _GPIO_CFG_H
#define _GPIO_CFG_H

/****************************************************************************
* 	                                           Include files
****************************************************************************/
#include <mach/gpio.h>


/****************************************************************************
* 	                                           Local Macros
****************************************************************************/
/*
modules:
#define  PIN_MODULE_FUNC	      ZX29_GPIO_XX
*/


/******************* Peripherals********************/

#if 0      /*For  Examples*/
/* Keypad */
#define	PIN_KEYPAD_KPD_POWER			    ZX29_GPIO_52
#define	PIN_KEYPAD_KPD_WPS				    ZX29_GPIO_75
#define	PIN_KEYPAD_KPD_RESET				ZX29_GPIO_76
#endif

/************************Platform********************/

/**************pmu******************/
#define PIN_PMU_INT			ZX29_GPIO_47
#define PIN_PMU_PSHOLD		ZX29_GPIO_24

#define PMU_INT_FUNC_SEL	GPIO47_EXT_INT0
#define PMU_PSHOLD_FUNC_SEL	GPIO24_GPIO24

/**************charge***************/
#ifdef CONFIG_ARCH_ZX297520V3_MIFI
#define PIN_CHARGE_INT							ZX29_GPIO_48
#define PIN_CHARGE_BOOST_GPIO1				ZX29_GPIO_35
#define PIN_CHARGE_BOOST_GPIO2				ZX29_GPIO_36
#define PIN_CHARGE_BOOST_GPIO3				ZX29_GPIO_37
#define PIN_CHARGE_BOOST_LOADSWITCH			ZX29_GPIO_38

#define CHARGE_INT_FUNC_SEL					GPIO48_EXT_INT1
#define CHARGE_BOOST_GPIO1_FUNC_SEL			GPIO35_GPIO35
#define CHARGE_BOOST_GPIO2_FUNC_SEL			GPIO36_GPIO36
#define CHARGE_BOOST_GPIO3_FUNC_SEL			GPIO37_GPIO37
#define CHARGE_BOOST_LOADSWITCH_FUNC_SEL	GPIO38_GPIO38
#elif defined CONFIG_ARCH_ZX297520V3_UFI
#define PIN_CHARGE_INT							ZX29_GPIO_48
#define PIN_CHARGE_BOOST_GPIO1				ZX29_GPIO_35
#define PIN_CHARGE_BOOST_GPIO2				ZX29_GPIO_36
#define PIN_CHARGE_BOOST_GPIO3				ZX29_GPIO_37
#define PIN_CHARGE_BOOST_LOADSWITCH			ZX29_GPIO_38

#define CHARGE_INT_FUNC_SEL					GPIO48_EXT_INT1
#define CHARGE_BOOST_GPIO1_FUNC_SEL			GPIO35_GPIO35
#define CHARGE_BOOST_GPIO2_FUNC_SEL			GPIO36_GPIO36
#define CHARGE_BOOST_GPIO3_FUNC_SEL			GPIO37_GPIO37
#define CHARGE_BOOST_LOADSWITCH_FUNC_SEL	GPIO38_GPIO38
#elif defined CONFIG_ARCH_ZX297520V3_FWP
#define PIN_CHARGE_EN_GPIO				ZX29_GPIO_40
#define PIN_CHARGE_STATE_GPIO			ZX29_GPIO_52
#define PIN_CHARGE_CTRL_GPIO			ZX29_GPIO_NULL
#define PIN_CHARGE_CHGIN_GPIO			ZX29_GPIO_49

#define CHARGE_EN_GPIO_SEL				GPIO40_GPIO40
#define CHARGE_STATE_GPIO_SEL			GPIO52_GPIO52
#define CHARGE_CTRL_GPIO_SEL			0xff//undefine
#define CHARGE_CHGIN_GPIO_SEL			GPIO49_GPIO49

#define CHARGE_STATE_FUNC_SEL			GPIO52_EXT_INT5
#define CHARGE_CHGIN_FUNC_SEL			GPIO49_EXT_INT2

#else
#define PIN_CHARGE_EN_GPIO				ZX29_GPIO_131
#define PIN_CHARGE_STATE_GPIO		ZX29_GPIO_54
#define PIN_CHARGE_CTRL_GPIO			ZX29_GPIO_130
#define PIN_CHARGE_CHGIN_GPIO		ZX29_GPIO_48

#define CHARGE_EN_GPIO_SEL				GPIO131_GPIO131
#define CHARGE_STATE_GPIO_SEL			GPIO54_GPIO54
#define CHARGE_CTRL_GPIO_SEL			GPIO130_GPIO130
#define CHARGE_CHGIN_GPIO_SEL			GPIO48_GPIO48

#define CHARGE_STATE_FUNC_SEL			GPIO54_EXT_INT7
#define CHARGE_CHGIN_FUNC_SEL			GPIO48_EXT_INT1
#endif
/***************lcd*****************/
#ifdef CONFIG_ARCH_ZX297520V3_FWP
#define PIN_LCD_BLG         ZX29_GPIO_53
#define LCD_BLG_FUNC_SEL	GPIO53_GPIO53
#define PIN_LCD_RST 		ZX29_GPIO_125
#define LCD_RST_FUNC_SEL	GPIO125_GPIO125
#else
#define PIN_LCD_BLG 		ZX29_GPIO_119
#define LCD_BLG_FUNC_SEL	GPIO119_GPIO119
#define PIN_LCD_RST 		ZX29_GPIO_120
#define LCD_RST_FUNC_SEL	GPIO120_GPIO120
#endif

#define PIN_SPI_DCX 		ZX29_GPIO_27
#define SPI_DCX_FUNC_SEL	GPIO27_GPIO27

#define PIN_SPI_CS			ZX29_GPIO_25
#define PIN_SPI_CLK			ZX29_GPIO_26
#define PIN_SPI_TXD			ZX29_GPIO_28

#define LCD_CS_SPI_FUNC_SEL		GPIO25_SSP0_CS
#define LCD_CLK_SPI_FUNC_SEL		GPIO26_SSP0_CLK
#define LCD_TXD_SPI_FUNC_SEL		GPIO28_SSP0_TXD

#define LCD_CS_GPIO_FUNC_SEL		GPIO25_GPIO25
#define LCD_CLK_GPIO_FUNC_SEL		GPIO26_GPIO26
#define LCD_TXD_GPIO_FUNC_SEL		GPIO28_GPIO28

#ifdef  CONFIG_FB_LCD_TE_ON
#define PIN_LCD_TE 		ZX29_GPIO_49
#define GPIO_TE_FUNC_SEL GPIO49_EXT_INT2
#define LCD_TE_INT   EX2_INT
#define LCD_TE_INT_LEVEL   INT_NEGEDGE
#endif

/***************led*****************/
#if defined(CONFIG_ARCH_ZX297520V3_CPE_SWITCH)
#define PIN_LED_POWER 			ZX29_GPIO_21
#define PIN_LED_LTE_RED			ZX29_GPIO_45
#define PIN_LED_LTE_BLUE		ZX29_GPIO_46
#define PIN_LED_WIFI 		 	ZX29_GPIO_86
#define PIN_LED_WPS 		 	ZX29_GPIO_72
#define PIN_LED_RJ11 			ZX29_GPIO_22
#define PIN_LED_4G_1 			ZX29_GPIO_29
#define PIN_LED_4G_2 			ZX29_GPIO_30
#define PIN_LED_4G_3 			ZX29_GPIO_73
#define PIN_LED_4G_4 			ZX29_GPIO_74
#define PIN_LED_4G_5 			ZX29_GPIO_75


#define LED_POWER_FUNC_SEL 		GPIO21_GPIO21
#define LED_LTE_RED_FUNC_SEL 	GPIO45_GPIO45
#define LED_LTE_BLUE_FUNC_SEL 	GPIO46_GPIO46
#define LED_WIFI_FUNC_SEL 		GPIO86_GPIO86
#define LED_WPS_FUNC_SEL		GPIO72_GPIO72
#define LED_RJ11_FUNC_SEL		GPIO22_GPIO22
#define LED_4G_1_FUNC_SEL		GPIO29_GPIO29
#define LED_4G_2_FUNC_SEL		GPIO30_GPIO30
#define LED_4G_3_FUNC_SEL		GPIO73_GPIO73
#define LED_4G_4_FUNC_SEL		GPIO74_GPIO74
#define LED_4G_5_FUNC_SEL		GPIO75_GPIO75

#elif defined(CONFIG_ARCH_ZX297520V3_MIFI)
#ifdef CONFIG_MIN_VERSION
#ifdef CONFIG_JCV_HW_MZ801_V1_2

#define PIN_LED_MODEM_RED 		ZX29_GPIO_124
#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_125
#define PIN_LED_WIFI 		 	ZX29_GPIO_39
#define PIN_LED_WIFI_GREEN 		 	ZX29_GPIO_40
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_41
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_42

#define LED_MODEM_RED_FUNC_SEL 		GPIO124_GPIO124
#define LED_MODEM_GREEN_FUNC_SEL 	GPIO125_GPIO125
#define LED_WIFI_FUNC_SEL 			GPIO39_GPIO39
#define LED_WIFI_GREEN_FUNC_SEL 			GPIO40_GPIO40
#define LED_BATTARY_RED_FUNC_SEL 	GPIO41_GPIO41
#define LED_BATTARY_GREEN_SEL 		GPIO42_GPIO42

#define PIN_SIM_SWITCH_A_CTRL              ZX29_GPIO_127
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO127_GPIO127
#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
//MZ804 128Mib nor flash for fota
#define PIN_LED_MODEM_RED 		ZX29_GPIO_61
#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_60
#define PIN_LED_WIFI_GREEN 		 	ZX29_GPIO_64
#define PIN_LED_WIFI 		 	ZX29_GPIO_65
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_63
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_62

#define LED_MODEM_RED_FUNC_SEL 		GPIO61_GPIO61
#define LED_MODEM_GREEN_FUNC_SEL 	GPIO60_GPIO60
#define LED_WIFI_GREEN_FUNC_SEL 			GPIO64_GPIO64
#define LED_WIFI_FUNC_SEL 			GPIO65_GPIO65
#define LED_BATTARY_RED_FUNC_SEL 	GPIO63_GPIO63
#define LED_BATTARY_GREEN_SEL 		GPIO62_GPIO62

//add by svk@20241122 for sim switch
#define PIN_SIM_SWITCH_A_CTRL              ZX29_GPIO_127
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO127_GPIO127


#else
#define PIN_LED_MODEM_RED 		ZX29_GPIO_124
#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_125
//#define PIN_LED_MODEM_BLUE 		ZX29_GPIO_123
#define PIN_LED_SMS 			ZX29_GPIO_40
#define PIN_LED_WIFI 		 	ZX29_GPIO_39
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_41
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_42

#define LED_MODEM_RED_FUNC_SEL 		GPIO124_GPIO124
#define LED_MODEM_GREEN_FUNC_SEL 	GPIO125_GPIO125
//#define LED_MODEM_BLUE_FUNC_SEL 	GPIO123_GPIO123
#define LED_SMS_FUNC_SEL 			GPIO40_GPIO40
#define LED_WIFI_FUNC_SEL 			GPIO39_GPIO39
#define LED_BATTARY_RED_FUNC_SEL 	GPIO41_GPIO41
#define LED_BATTARY_GREEN_SEL 		GPIO42_GPIO42
#endif
#else
#define PIN_LED_MODEM_RED 		ZX29_GPIO_124
//#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_44
#define PIN_LED_MODEM_BLUE 		ZX29_GPIO_123
#define PIN_LED_SMS 			ZX29_GPIO_40
#define PIN_LED_WIFI 		 	ZX29_GPIO_39
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_42/*red and green changed*/
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_41

#define LED_MODEM_RED_FUNC_SEL 		GPIO124_GPIO124
//#define LED_MODEM_GREEN_FUNC_SEL 	GPIO44_GPIO44
#define LED_MODEM_BLUE_FUNC_SEL 	GPIO123_GPIO123
#define LED_SMS_FUNC_SEL 			GPIO40_GPIO40
#define LED_WIFI_FUNC_SEL 			GPIO39_GPIO39
#define LED_BATTARY_RED_FUNC_SEL 	GPIO42_GPIO42
#define LED_BATTARY_GREEN_SEL 		GPIO41_GPIO41
#endif/*CONFIG_MIN_VERSION*/

#elif defined(CONFIG_ARCH_ZX297520V3_UFI)
#ifdef CONFIG_MIN_VERSION
#if defined(CONFIG_JCV_HW_UZ901_V1_4)
#define PIN_LED_MODEM_RED 		  ZX29_GPIO_124
#define PIN_LED_MODEM_GREEN 	  ZX29_GPIO_125
#define PIN_LED_WIFI_GREEN 		  ZX29_GPIO_123
#define PIN_SIM_SWITCH_A_CTRL   ZX29_GPIO_127

#define LED_MODEM_RED_FUNC_SEL 		      GPIO124_GPIO124
#define LED_MODEM_GREEN_FUNC_SEL 	      GPIO125_GPIO125
#define LED_WIFI_GREEN_FUNC_SEL 			  GPIO123_GPIO123
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO127_GPIO127

#if defined(CONFIG_JCV_HW_UZ901_V1_6) || defined(CONFIG_JCV_HW_UZ901_V2_5)
#define PIN_SIM_HOTSWAP   ZX29_GPIO_40
#define PIN_SIM_HOTSWAP_FUNC_SEL 	GPIO40_GPIO40
#endif

#elif defined(CONFIG_JCV_HW_POWERBANK_DZ801)
#define PIN_LED_MODEM_RED 		  ZX29_GPIO_124
#define PIN_LED_MODEM_GREEN 	  ZX29_GPIO_125
#define PIN_LED_MODEM_BLUE 	    ZX29_GPIO_123
#define PIN_LED_WIFI_GREEN 		  ZX29_GPIO_39
#define PIN_SIM_SWITCH_A_CTRL   ZX29_GPIO_126

#define LED_MODEM_RED_FUNC_SEL 		      GPIO124_GPIO124
#define LED_MODEM_GREEN_FUNC_SEL 	      GPIO125_GPIO125
#define LED_MODEM_BLUE_FUNC_SEL 	      GPIO123_GPIO123
#define LED_WIFI_GREEN_FUNC_SEL 			  GPIO39_GPIO39
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO126_GPIO126

#ifdef CONFIG_JCV_HW_DZ802_V1_0
#define PIN_SIM_HOTSWAP   ZX29_GPIO_40
#define PIN_SIM_HOTSWAP_FUNC_SEL 	GPIO40_GPIO40
#elif defined(CONFIG_JCV_HW_DZ803_V1)
#define PIN_SIM_HOTSWAP   ZX29_GPIO_40
#define PIN_SIM_HOTSWAP_FUNC_SEL 	GPIO40_GPIO40

#define PIN_POWERBANK_EN   ZX29_GPIO_131
#define PIN_POWERBANK_EN_FUNC_SEL 	GPIO131_GPIO131
#endif
#elif defined(CONFIG_JCV_HW_GS28V_V1)
#define PIN_LED_MODEM_RED 		  ZX29_GPIO_123
#define PIN_LED_MODEM_GREEN 		  ZX29_GPIO_39
#define PIN_SIM_SWITCH_A_CTRL   ZX29_GPIO_127

#define LED_MODEM_RED_FUNC_SEL 		      GPIO123_GPIO123
#define LED_MODEM_GREEN_FUNC_SEL 		      GPIO39_GPIO39
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO127_GPIO127

#elif defined(CONFIG_JCV_HW_MZ901_V_0)
#define PIN_LED_MODEM_RED 		  ZX29_GPIO_63
#define PIN_LED_MODEM_GREEN 	  ZX29_GPIO_64
#define PIN_LED_WIFI_GREEN 		  ZX29_GPIO_55
#define PIN_SIM_SWITCH_A_CTRL   ZX29_GPIO_126

#define PIN_LED_BATTARY_PERCENT_25 		  ZX29_GPIO_59
#define PIN_LED_BATTARY_PERCENT_50 	  ZX29_GPIO_60
#define PIN_LED_BATTARY_PERCENT_75 		  ZX29_GPIO_61
#define PIN_LED_BATTARY_PERCENT_100 		  ZX29_GPIO_62

#define LED_MODEM_RED_FUNC_SEL 		      GPIO63_GPIO63
#define LED_MODEM_GREEN_FUNC_SEL 	      GPIO64_GPIO64
#define LED_WIFI_GREEN_FUNC_SEL 			  GPIO55_GPIO55
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO126_GPIO126

#define LED_BATTARY_PERCENT_25_FUNC_SEL 			  GPIO59_GPIO59
#define LED_BATTARY_PERCENT_50_FUNC_SEL 			  GPIO60_GPIO60
#define LED_BATTARY_PERCENT_75_FUNC_SEL 			  GPIO61_GPIO61
#define LED_BATTARY_PERCENT_100_FUNC_SEL 			  GPIO62_GPIO62

#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
#define PIN_LED_MODEM_RED 		ZX29_GPIO_61
#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_60
#define PIN_LED_WIFI_GREEN 		 	ZX29_GPIO_64
#define PIN_LED_WIFI 		 	ZX29_GPIO_65
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_63
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_62

#define LED_MODEM_RED_FUNC_SEL 		GPIO61_GPIO61
#define LED_MODEM_GREEN_FUNC_SEL 	GPIO60_GPIO60
#define LED_WIFI_GREEN_FUNC_SEL 			GPIO64_GPIO64
#define LED_WIFI_FUNC_SEL 			GPIO65_GPIO65
#define LED_BATTARY_RED_FUNC_SEL 	GPIO63_GPIO63
#define LED_BATTARY_GREEN_SEL 		GPIO62_GPIO62

//add by svk@20241122 for sim switch
#define PIN_SIM_SWITCH_A_CTRL              ZX29_GPIO_127
#define PIN_SIM_SWITCH_A_CTRL_FUNC_SEL 	GPIO127_GPIO127
#else
#define PIN_LED_MODEM_RED 		ZX29_GPIO_124
#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_125
//#define PIN_LED_MODEM_BLUE 		ZX29_GPIO_123
#define PIN_LED_SMS 			ZX29_GPIO_40
#define PIN_LED_WIFI 		 	ZX29_GPIO_39
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_41
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_42

#define LED_MODEM_RED_FUNC_SEL 		GPIO124_GPIO124
#define LED_MODEM_GREEN_FUNC_SEL 	GPIO125_GPIO125
//#define LED_MODEM_BLUE_FUNC_SEL 	GPIO123_GPIO123
#define LED_SMS_FUNC_SEL 			GPIO40_GPIO40
#define LED_WIFI_FUNC_SEL 			GPIO39_GPIO39
#define LED_BATTARY_RED_FUNC_SEL 	GPIO41_GPIO41
#define LED_BATTARY_GREEN_SEL 		GPIO42_GPIO42
#endif
#else
#define PIN_LED_MODEM_RED 		ZX29_GPIO_124
//#define PIN_LED_MODEM_GREEN 	ZX29_GPIO_44
#define PIN_LED_MODEM_BLUE 		ZX29_GPIO_123
#define PIN_LED_SMS 			ZX29_GPIO_40
#define PIN_LED_WIFI 		 	ZX29_GPIO_39
#define PIN_LED_BATTARY_RED 	ZX29_GPIO_42/*red and green changed*/
#define PIN_LED_BATTARY_GREEN 	ZX29_GPIO_41

#define LED_MODEM_RED_FUNC_SEL 		GPIO124_GPIO124
//#define LED_MODEM_GREEN_FUNC_SEL 	GPIO44_GPIO44
#define LED_MODEM_BLUE_FUNC_SEL 	GPIO123_GPIO123
#define LED_SMS_FUNC_SEL 			GPIO40_GPIO40
#define LED_WIFI_FUNC_SEL 			GPIO39_GPIO39
#define LED_BATTARY_RED_FUNC_SEL 	GPIO42_GPIO42
#define LED_BATTARY_GREEN_SEL 		GPIO41_GPIO41
#endif/*CONFIG_MIN_VERSION*/

#else
#ifdef _V3PHONE_TYPE_C31F_

#define PIN_LED_FLASHLIGHT			ZX29_GPIO_121
#define PIN_LED_MOTOR				ZX29_GPIO_125
#define LED_FLASHLIGHT_FUNC_SEL	GPIO121_GPIO121
#define LED_MOTOR_FUNC_SEL		GPIO125_GPIO125
#else
//#define PIN_LED_FLASHLIGHT			ZX29_GPIO_45
#define PIN_LED_MOTOR				ZX29_GPIO_120

//#define LED_FLASHLIGHT_FUNC_SEL	GPIO45_GPIO45
#define LED_MOTOR_FUNC_SEL		GPIO120_GPIO120
#endif
#endif
/***************TP********************/
#define PIN_TP_IRQ			ZX29_GPIO_51
#define PIN_TP_RST			ZX29_GPIO_121
#define TP_IRQ_FUN_SEL		GPIO51_EXT_INT4
#define TP_RST_GPIO_SEL		GPIO121_GPIO121

/***************kpd*****************/
/*Keypad (power_on, ufi, ufi_reset) for ufi*/
#if defined(CONFIG_ARCH_ZX297520V3_CPE_SWITCH)
#define PIN_KPD_WIFI 		ZX29_GPIO_130
#define PIN_KPD_WPS 		ZX29_GPIO_131
#define PIN_KPD_RST 		ZX29_GPIO_49

#define KPD_WIFI_FUNC_GPIO  GPIO130_GPIO130
#define KPD_WPS_FUNC_GPIO 	GPIO131_GPIO131
#define KPD_RST_FUNC_GPIO 	GPIO49_GPIO49

#define KPD_WIFI_FUNC_INT	GPIO130_EXT_INT11
#define KPD_WPS_FUNC_INT 	GPIO131_EXT_INT12
#define KPD_RST_FUNC_INT	GPIO49_EXT_INT2
#else
#define PIN_KPD_WPS 		ZX29_GPIO_131
#define PIN_KPD_RST 		ZX29_GPIO_126
/* #define KPD_POWER_FUNC_GPIO GPIO52_GPIO52 */
#define KPD_WPS_FUNC_GPIO 	GPIO131_GPIO131
#define KPD_RST_FUNC_GPIO 	GPIO126_GPIO126

/* #define KPD_POWER_FUNC_INT	GPIO52_EXT_INT2 */
#define KPD_WPS_FUNC_INT 	GPIO131_EXT_INT12
#define KPD_RST_FUNC_INT	GPIO126_EXT_INT15

#endif

/********************High-speed transaction***********/
/* SD */
#define PIN_MMC_TF_CARD_DET ZX29_GPIO_51
#define PIN_MMC_TF_CARD_DET_FUNC GPIO51_EXT_INT4
#define PIN_MMC_TF_CARD_DET_GPIO_FUNC GPIO51_GPIO51

/*WIFI*/
/*-----------------------------------------------------------------------
                      BUCK_GPIO     |    PWR_CTL     |     CHIP_ENABLE   |    WAKEUP
-------------------------------------------------------------------------
 CPE            ZX29_GPIO_77      ZX29_GPIO_42      ZX29_GPIO_71     ZX29_GPIO_57
-------------------------------------------------------------------------
 EVB                     --                ZX29_GPIO_27      ZX29_GPIO_71     ZX29_GPIO_57
-------------------------------------------------------------------------
 MDL            ZX29_GPIO_77      ZX29_GPIO_42      ZX29_GPIO_71     ZX29_GPIO_57
-------------------------------------------------------------------------
 MIFI                    --                ZX29_GPIO_27      ZX29_GPIO_71     ZX29_GPIO_57
------------------------------------------------------------------------*/
//SOC bug, GPIO77 must be configed GPIO function before buck3v3 power on..
#define PIN_WIFI_PWR_BUCK_GPIO			ZX29_GPIO_77
#define PIN_WIFI_PWR_BUCK_GPIO_SEL		GPIO77_GPIO77

//WIFI power control pin, EVB is 27.
#define PIN_WIFI_PWR_CTL					ZX29_GPIO_121
#define PIN_WIFI_PWR_CTL_SEL				GPIO121_GPIO121

//WIFI chip enable pin.
#ifdef CONFIG_ARCH_ZX297520V3_MIFI
#define PIN_WIFI_CHIP_ENABLE				ZX29_GPIO_85
#define PIN_WIFI_CHIP_ENABLE_SEL			GPIO85_GPIO85
#elif defined (CONFIG_ARCH_ZX297520V3_UFI)
#define PIN_WIFI_CHIP_ENABLE				ZX29_GPIO_85
#define PIN_WIFI_CHIP_ENABLE_SEL			GPIO85_GPIO85
#elif defined (CONFIG_ARCH_ZX297520V3_CPE)
#define PIN_WIFI_CHIP_ENABLE				ZX29_GPIO_120
#define PIN_WIFI_CHIP_ENABLE_SEL			GPIO120_GPIO120
#elif defined (CONFIG_ARCH_ZX297520V3_CPE_SWITCH)
#define PIN_WIFI_CHIP_ENABLE				ZX29_GPIO_123
#define PIN_WIFI_CHIP_ENABLE_SEL			GPIO123_GPIO123
#else
#define PIN_WIFI_CHIP_ENABLE				ZX29_GPIO_31
#define PIN_WIFI_CHIP_ENABLE_SEL			GPIO31_GPIO31
#endif

//WIFI wakeup pin
#define PIN_WIFI_WAKEUP					ZX29_GPIO_54
#define PIN_WIFI_WAKEUP_SEL				GPIO54_EXT_INT7

//interrupte of wakeup pin.
#define INT_WIFI_WAKEUP					PCU_EX7_INT


/********************Low-speed transaction***********/

/* UART */
#define PIN_UART0_RXD     ZX29_GPIO_29	/*GPIO 29 / 30*/
#define FNC_UART0_RXD	  GPIO29_UART0_RXD
#define PIN_UART0_TXD     ZX29_GPIO_30	/*GPIO 30 / 29*/
#define FNC_UART0_TXD	  GPIO30_UART0_TXD
#define PIN_UART0_CTS     ZX29_GPIO_31	/*GPIO 31 / 120*/
#define FNC_UART0_CTS     GPIO31_UART0_CTS
#define PIN_UART0_RTS     ZX29_GPIO_32	/*GPIO 32 / 119*/
#define FNC_UART0_RTS     GPIO32_UART0_RTS

#define PIN_UART1_RXD     ZX29_GPIO_33   /*GPIO 33 / 32*/
#define FNC_UART1_RXD	  GPIO33_UART1_RXD
#define PIN_UART1_TXD     ZX29_GPIO_34   /*GPIO 34 / 31*/
#define FNC_UART1_TXD	  GPIO34_UART1_TXD
#define PIN_UART1_CTS     ZX29_GPIO_126  /*GPIO 126*/
#define FNC_UART1_CTS	  GPIO126_UART1_CTS
#define PIN_UART1_RTS     ZX29_GPIO_125   /*GPIO 125*/
#define FNC_UART1_RTS	  GPIO125_UART1_RTS

#define PIN_UART2_RXD     ZX29_GPIO_121  /*GPIO 121 / 33 /34*/
#define FNC_UART2_RXD	  GPIO121_UART2_RXD
#define PIN_UART2_TXD     ZX29_GPIO_122  /*GPIO 122 / 34 /33*/
#define FNC_UART2_TXD	  GPIO122_UART2_TXD
#define PIN_UART2_CTS     ZX29_GPIO_124  /*GPIO 124*/
#define FNC_UART2_CTS	  GPIO124_UART2_CTS
#define PIN_UART2_RTS     ZX29_GPIO_123  /*GPIO 123*/
#define FNC_UART2_RTS	  GPIO123_UART2_RTS

/************* I2S ***************/
#define PIN_I2S0_WS		  	ZX29_GPIO_35
#define PIN_I2S0_CLK		ZX29_GPIO_36
#define PIN_I2S0_DIN		ZX29_GPIO_37
#define PIN_I2S0_DOUT		ZX29_GPIO_38

#define PIN_I2S1_WS		  	ZX29_GPIO_39
#define PIN_I2S1_CLK		ZX29_GPIO_40
#define PIN_I2S1_DIN		ZX29_GPIO_41
#define PIN_I2S1_DOUT		ZX29_GPIO_42

#define FUN_I2S0_WS			GPIO35_I2S0_WS
#define FUN_I2S0_CLK		GPIO36_I2S0_CLK
#define FUN_I2S0_DIN		GPIO37_I2S0_DIN
#define FUN_I2S0_DOUT		GPIO38_I2S0_DOUT

#define FUN_I2S1_WS		    GPIO39_I2S1_WS
#define FUN_I2S1_CLK		GPIO40_I2S1_CLK
#define FUN_I2S1_DIN		GPIO41_I2S1_DIN
#define FUN_I2S1_DOUT		GPIO42_I2S1_DOUT

/************* TDM ***************/
#ifdef CONFIG_ARCH_ZX297520V3_CPE_SWITCH

#define PIN_TDM_FS		  	ZX29_GPIO_39
#define PIN_TDM_CLK			ZX29_GPIO_40
#define PIN_TDM_DIN			ZX29_GPIO_41
#define PIN_TDM_DOUT		ZX29_GPIO_42

#define FUN_TDM_FS			GPIO39_TDM_FS
#define FUN_TDM_CLK			GPIO40_TDM_CLK
#define FUN_TDM_DIN			GPIO41_TDM_DATA_IN
#define FUN_TDM_DOUT		GPIO42_TDM_DATA_OUT

#else
#define PIN_TDM_FS		  	ZX29_GPIO_35
#define PIN_TDM_CLK			ZX29_GPIO_36
#define PIN_TDM_DIN			ZX29_GPIO_37
#define PIN_TDM_DOUT		ZX29_GPIO_38

#define FUN_TDM_FS			GPIO35_TDM_FS
#define FUN_TDM_CLK			GPIO36_TDM_CLK
#define FUN_TDM_DIN			GPIO37_TDM_DATA_IN
#define FUN_TDM_DOUT		GPIO38_TDM_DATA_OUT
#endif

/************* CODEC ***************/
#define CODEC_EN            ZX29_GPIO_123
#define CODEC_REFCLK        ZX29_GPIO_15
#define CODEC_RESET         ZX29_GPIO_122
#define PIN_EARP_INT        ZX29_GPIO_53
#define EARP_INT_FUNC_SEL   GPIO53_EXT_INT6

/************* SLIC ***************/
#ifdef CONFIG_ARCH_ZX297520V3_CPE_SWITCH
#define SLIC_PWR_CTRL       ZX29_GPIO_132
#endif

#endif
