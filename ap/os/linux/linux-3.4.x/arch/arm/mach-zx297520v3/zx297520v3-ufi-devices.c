/*
 * arch/arm/mach-zx297520v3/zx297520v3_devices.c
 *
 *  Copyright (C) 2015 ZTE-TSP
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 */
#include <asm/mach/arch.h>
#include <asm/mach/map.h>

#include <linux/dma-mapping.h>
#include <linux/platform_device.h>
#include <linux/soc/zte/rpm/rpmsg.h>
#include <linux/i2c.h>

#include <mach/dma.h>
#include <mach/board.h>
#include <mach/iomap.h>
#include <mach/irqs.h>
#include <mach/i2c.h>
#include <mach/gpio.h>
#include <mach/zx29_mmc.h>
#include <mach/zx29_uart_def.h>
#include <linux/soc/zte/tsc/tsc.h>

#if (defined CONFIG_SPI_ZX29) || (defined CONFIG_SPI_GPIO)
#include <linux/spi/spi.h>
#include <linux/spi/spi_gpio.h>
#include <mach/spi.h>
#include <linux/video/zx29_lcd.h>
#endif

#ifdef CONFIG_CHARGER_ZX234502
#include <linux/power/zx234502_charger.h>
#endif
#ifdef CONFIG_CHARGER_AW3215
#include <linux/power/aw3215_charger.h>
#endif
#ifdef CONFIG_LEDS_GPIO
#include <linux/leds.h>
#endif
#ifdef CONFIG_MMC_ZX29
#include <linux/mmc/host.h>
#endif
#ifdef CONFIG_KEYBOARD_ZX_INT
#include <linux/input.h>
#include <linux/gpio_keys.h>
#endif
#ifdef CONFIG_KEYBOARD_ZX_5x6
#include <linux/input.h>
#include <linux/input/zx29_keypad_5x6.h>
#endif

#ifdef CONFIG_MFD_ZX234290_I2C
#include <linux/mfd/zx234290.h>
#endif

#if (defined CONFIG_SND_SOC_ZX297520V3) || (defined CONFIG_SND_SOC_ZX297520V3_MODULE)
#include <sound/zx29_snd_platform.h>
#endif
#include <mach/gpio_cfg.h>


struct zx29_uart_platdata  zx29_uart0_platdata= {
		.uart_use = 1,
		.uart_rxd.gpioname = "uart0_rxd",
		.uart_rxd.gpionum = PIN_UART0_RXD,
		.uart_rxd.gpiofnc = FNC_UART0_RXD,
		.uart_txd.gpioname = "uart0_txd",
		.uart_txd.gpionum = PIN_UART0_TXD,
		.uart_txd.gpiofnc = FNC_UART0_TXD,
		.uart_ctsrtsuse = 0,
		.uart_cts.gpioname = "uart0_cts",
		.uart_cts.gpionum= PIN_UART0_CTS ,
		.uart_cts.gpiofnc = FNC_UART0_CTS ,
		.uart_rts.gpioname = "uart0_rts",
		.uart_rts.gpionum =PIN_UART0_RTS,
		.uart_rts.gpiofnc = FNC_UART0_RTS,
		.uart_abauduse = 0,
		.uart_input_enable = 0,
};
struct zx29_uart_platdata  zx29_uart1_platdata= {
		.uart_use = 1,
		.uart_rxd.gpioname = "uart1_rxd",
		.uart_rxd.gpionum = PIN_UART1_RXD,
		.uart_rxd.gpiofnc = FNC_UART1_RXD,
		.uart_txd.gpioname = "uart1_txd",
		.uart_txd.gpionum = PIN_UART1_TXD,
		.uart_txd.gpiofnc = FNC_UART1_TXD,
		.uart_ctsrtsuse = 0,
		.uart_cts.gpioname = "uart1_cts",
		.uart_cts.gpionum= PIN_UART1_CTS ,
		.uart_cts.gpiofnc = FNC_UART1_CTS ,
		.uart_rts.gpioname = "uart1_rts",
		.uart_rts.gpionum = PIN_UART1_RTS,
		.uart_rts.gpiofnc = FNC_UART1_RTS,
		.uart_abauduse = 0,
		.uart_input_enable = 0,
};
struct zx29_uart_platdata  zx29_uart2_platdata= {
		.uart_use = 0,
		.uart_rxd.gpioname = "uart2_rxd",
		.uart_rxd.gpionum = PIN_UART2_RXD,
		.uart_rxd.gpiofnc = FNC_UART2_RXD,
		.uart_txd.gpioname = "uart2_txd",
		.uart_txd.gpionum = PIN_UART2_TXD,
		.uart_txd.gpiofnc = FNC_UART2_TXD,
		.uart_ctsrtsuse = 0,
		.uart_cts.gpioname = "uart2_cts",
		.uart_cts.gpionum= PIN_UART2_CTS ,
		.uart_cts.gpiofnc = FNC_UART2_CTS ,
		.uart_rts.gpioname = "uart2_rts",
		.uart_rts.gpionum = PIN_UART2_RTS,
		.uart_rts.gpiofnc = FNC_UART2_RTS,
		.uart_abauduse = 0,
		.uart_input_enable = 0,
};
/* --------------------------------------------------------------------
 *  UART
 * -------------------------------------------------------------------- */
#ifdef CONFIG_SERIAL_ZX29_UART
/* UART0*/
static struct resource zx29_uart0_resources[] = {
	[0] = {
		.start	= ZX29_UART0_PHYS,
		.end	= ZX29_UART0_PHYS + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= UART0_MIX_INT,
		.end	= UART0_MIX_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_uart0_device = {
	.name		= "zx29_uart",
	.id		= 0,
	.resource	= zx29_uart0_resources,
	.num_resources	= ARRAY_SIZE(zx29_uart0_resources),
	.dev = {
		.platform_data = &zx29_uart0_platdata,
	}
};
/* UART2*/
static struct resource zx29_uart1_resources[] = {
	[0] = {
		.start	= ZX29_UART1_PHYS,
		.end	= ZX29_UART1_PHYS + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= UART1_MIX_INT,
		.end	= UART1_MIX_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_uart1_device = {
	.name		= "zx29_uart",
	.id		= 1,
	.resource	= zx29_uart1_resources,
	.num_resources	= ARRAY_SIZE(zx29_uart1_resources),
	.dev = {
		.platform_data = &zx29_uart1_platdata,
	}
};

/* UART2*/
static struct resource zx29_uart2_resources[] = {
	[0] = {
		.start	= ZX29_UART2_PHYS,
		.end	= ZX29_UART2_PHYS + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= UART2_MIX_INT,
		.end	= UART2_MIX_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_uart2_device = {
	.name		= "zx29_uart",
	.id		= 2,
	.resource	= zx29_uart2_resources,
	.num_resources	= ARRAY_SIZE(zx29_uart2_resources),
	.dev = {
		.platform_data = & zx29_uart2_platdata,
	}
};
#endif

/* --------------------------------------------------------------------
 *	DMA -- Direct Memory Access
* -------------------------------------------------------------------- */
#ifdef CONFIG_ZX29_DMA
static struct resource zx29_dma_res[] = {
	[0] = {
		.start	= (u32)ZX_DMA_PS_BASE,
		.end	= (u32)ZX_DMA_PS_BASE + SZ_4K - 1,
		.flags  = IORESOURCE_MEM,
	},
	[1] = {
		.start = PS_DMA_INT,
		.end   = PS_DMA_INT,
		.flags = IORESOURCE_IRQ,
	},
};
static struct platform_device zx29_dma_device = {
	.name = "zx29_dma",
	.id = 0,
	.resource = zx29_dma_res,
	.num_resources	= ARRAY_SIZE(zx29_dma_res),
};
#endif

#if (defined CONFIG_SND_SOC_ZX297520V3) || (defined CONFIG_SND_SOC_ZX297520V3_MODULE)

static struct platform_device zx29_audio = {
	.name		= SND_MACHINE_PDEV_NAME,
	.id		= -1,
	.dev		= {
		.platform_data	= ZX29_SND_MACH_PDATA, //&snd_machine_pdata,
	},
};
#endif

#if (defined CONFIG_SND_SOC_ZX_PCM) || (defined CONFIG_SND_SOC_ZX_PCM_MODULE)
/* ASOC DMA */
static unsigned long long zx29_device_dma_mask = DMA_BIT_MASK(32);

struct platform_device zx29_asoc_dma = {
	.name		= "zx29-pcm-audio",
	.id		= -1,
	.dev		= {
		.dma_mask		= &zx29_device_dma_mask,
		.coherent_dma_mask	= DMA_BIT_MASK(32),
	}
};
#endif

/* --------------------------------------------------------------------
 *	I2S
* -------------------------------------------------------------------- */
//#ifdef CONFIG_SND_SOC_ZX_I2S
#if (defined CONFIG_SND_SOC_ZX_I2S) || (defined CONFIG_SND_SOC_ZX_I2S_MODULE)
#define zx29_I2S0	1
//#define zx29_I2S1	1

#ifdef zx29_I2S0
/* I2S0 */
static struct resource i2s0_res[] = {
	[0] = {
		.start	= (u32)ZX_I2S0_BASE,
		.end	= (u32)ZX_I2S0_BASE + SZ_4K - 1,
		.flags  = IORESOURCE_MEM,
	},
	[1] = {
		.start = DMA_CH_I2S0_TX,
		.end   = DMA_CH_I2S0_TX,
		.flags = IORESOURCE_DMA,
	},
	[2] = {
		.start = DMA_CH_I2S0_RX0,
		.end   = DMA_CH_I2S0_RX0,
		.flags = IORESOURCE_DMA,
	},
};
static struct platform_device zx29_i2s0_device = {
	.name = "zx29_i2s",
	.id = 0,
	.resource = i2s0_res,
	.num_resources	= ARRAY_SIZE(i2s0_res),
};
#endif

#ifdef zx29_I2S1
static struct resource i2s1_res[] = {
	[0] = {
		.start	= (u32)ZX_I2S1_BASE,
		.end	= (u32)ZX_I2S1_BASE + SZ_4K - 1,
		.flags  = IORESOURCE_MEM,
	},
	[1] = {
		.start = DMA_CH_I2S1_TX,
		.end   = DMA_CH_I2S1_TX,
		.flags = IORESOURCE_DMA,
	},
	[2] = {
		.start = DMA_CH_I2S1_RX0,
		.end   = DMA_CH_I2S1_RX0,
		.flags = IORESOURCE_DMA,
	},
};
static struct platform_device zx29_i2s1_device = {
	.name = "zx29_i2s",
	.id = 1,
	.resource = i2s1_res,
	.num_resources	= ARRAY_SIZE(i2s1_res),
};
#endif
#endif

#if (defined CONFIG_SND_SOC_ZX_VOICE) || (defined CONFIG_SND_SOC_ZX_VOICE_MODULE)
static struct platform_device voice_asoc_device = {
	.name = "voice_audio",
	.id = -1,
};
#endif
/* --------------------------------------------------------------------
 *  MMC / SD
 * -------------------------------------------------------------------- */
#ifdef CONFIG_MMC_ZX29
static struct resource zx29_sdmmc0_resources[] = {
	[0] = {
		.start	= ZX_SD0_BASE,
		.end	= ZX_SD0_BASE + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= SD0_INT,
		.end	= SD0_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

struct dw_mci_board zx29_sdmmc0_platdata = {
	.num_slots	= 1,
#if 1//def CONFIG_RTL8192CD
	.quirks = DW_MCI_QUIRK_BROKEN_CARD_DETECTION | DW_MCI_QUIRK_SDIO \
	| DW_MCI_QUIRK_UNALIGN_DMA_SZ | DW_MCI_QUIRK_UNALIGN_DMA_START |DW_MCI_QUIRK_CLK_PHASE_TURN ,
	.caps	= (MMC_CAP_4_BIT_DATA | MMC_CAP_MMC_HIGHSPEED | MMC_CAP_SD_HIGHSPEED |MMC_CAP_UHS_SDR50 | MMC_CAP_SDIO_IRQ |MMC_CAP_NONREMOVABLE),
#else
	.quirks = DW_MCI_QUIRK_BROKEN_CARD_DETECTION | DW_MCI_QUIRK_SDIO,
	.caps	= (MMC_CAP_4_BIT_DATA | MMC_CAP_MMC_HIGHSPEED | MMC_CAP_SD_HIGHSPEED |MMC_CAP_UHS_SDR50),
#endif

#ifdef CONFIG_SSV6X5X
	.bus_hz = 50*1000*1000,
#else
	.bus_hz = 100*1000*1000,
#ifdef CONFIG_AIC8800
	.bus_hz = 100 *1000*1000,
#endif
#endif
	.pm_caps = MMC_PM_KEEP_POWER | MMC_PM_IGNORE_PM_NOTIFY,
	.data1_irq = SD0_DATA1_INT,
};
static struct platform_device zx29_sdmmc0_device = {
	.name		= "zx29_sd",
	.id		= 0,
	.resource	= zx29_sdmmc0_resources,
	.num_resources	= ARRAY_SIZE(zx29_sdmmc0_resources),
	.dev		= {
		.coherent_dma_mask	= 0xffffffffUL,
		.platform_data		= &zx29_sdmmc0_platdata,
	},
};
static struct resource zx29_sdmmc1_resources[] = {
	[0] = {
		.start	= ZX_SD1_BASE,
		.end	= ZX_SD1_BASE + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= SD1_INT,
		.end	= SD1_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

struct dw_mci_board zx29_sdmmc1_platdata = {
	.num_slots	= 1,
#ifdef CONFIG_XR_WLAN
	.quirks = DW_MCI_QUIRK_BROKEN_CARD_DETECTION | DW_MCI_QUIRK_SDIO \
	| DW_MCI_QUIRK_UNALIGN_DMA_SZ | DW_MCI_QUIRK_UNALIGN_DMA_START,
	.bus_hz = 100*1000*1000,
	.caps	= (MMC_CAP_4_BIT_DATA | MMC_CAP_MMC_HIGHSPEED | MMC_CAP_SD_HIGHSPEED |MMC_CAP_UHS_SDR50 | MMC_CAP_SDIO_IRQ|MMC_CAP_NONREMOVABLE),
	.pm_caps = MMC_PM_KEEP_POWER | MMC_PM_IGNORE_PM_NOTIFY,
	.data1_irq = SD1_DATA1_INT,
#else
     .bus_hz = 50*1000*1000,
	 .caps	= (MMC_CAP_4_BIT_DATA | MMC_CAP_MMC_HIGHSPEED | MMC_CAP_SD_HIGHSPEED),
     .detect_delay_ms = 500,
#endif
	//.init = sdmmc_init,
	//.setpower = sdmmc_set_power,

	//.detect_delay_ms = 500,
};


static struct platform_device zx29_sdmmc1_device = {
	.name		= "zx29_sd",
	.id		= 1,
	.resource	= zx29_sdmmc1_resources,
	.num_resources	= ARRAY_SIZE(zx29_sdmmc1_resources),
	.dev		= {
		.coherent_dma_mask	= 0xffffffffUL,
		.platform_data		= &zx29_sdmmc1_platdata,
	},
};
#endif

/* --------------------------------------------------------------------
 *  NAND
 * -------------------------------------------------------------------- */
#ifdef CONFIG_MTD_ZXIC_SPIFC
 static struct resource spi_nand_resource[] = {
	  [0] = {
		  .start  = ZX_SPIFC0_BASE,
		  .end	  = ZX_SPIFC0_BASE + SZ_4K - 1,
		  .flags  = IORESOURCE_MEM,
		  .name   = "spifc_reg",
	  },
	  [2] = {
		  .start  = SPI_FC0_INT,
		  .end	  = SPI_FC0_INT,
		  .flags  = IORESOURCE_IRQ,

	  },
  };
#endif
#ifdef CONFIG_MTD_NAND_DENALI
static struct resource denali_nand_resource[] = {
	[0] = {
		.start	= ZX_NAND_REG_BASE,
		.end	= ZX_NAND_REG_BASE + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
		.name   = "denali_reg",
	},
	[1] = {
		.start	= ZX_NAND_DATA_BASE,
		.end	= ZX_NAND_DATA_BASE + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
		.name   = "nand_data",
	},
    [2] = {
		.start	= NAND_INT,
		.end	= NAND_INT,
		.flags	= IORESOURCE_IRQ,

	},
};
struct denali_nand_data {
	struct mtd_partition *parts;
	int (*dev_ready)(struct mtd_info *mtd);
	u32 nr_parts;
	u8 ale;		/* address line number connected to ALE */
	u8 cle;		/* address line number connected to CLE */
	u8 width;	/* buswidth */
	u8 chip_delay;
};
static struct denali_nand_data zx29_nand_data = {
	.cle		= 0,
	.ale		= 1,
	.width		= 8,
};
struct platform_device zx29_device_nand = {
	.name		= "denali-nand-dt",
	.id		= -1,
	.dev		= {
		.platform_data	= &zx29_nand_data,
	},
	.num_resources	= ARRAY_SIZE(denali_nand_resource),
	.resource	= denali_nand_resource,
};
#endif

#ifdef CONFIG_MTD_ZXIC_SPIFC
struct platform_device zx29_device_spi_nand = {
	.name		= "spi-nand-dt",
	.id		= -1,
	.num_resources	= ARRAY_SIZE(spi_nand_resource),
	.resource	= spi_nand_resource,
};
#endif

/*
 *--------------------------------------------------------------------
 *  						NOR
 * --------------------------------------------------------------------
 */

#ifdef CONFIG_SPI_ZXIC_NOR
 static struct resource spi_nor_resource[] = {
	  [0] = {
		  .start  = ZX_SPIFC0_BASE,
		  .end	  = ZX_SPIFC0_BASE + SZ_4K - 1,
		  .flags  = IORESOURCE_MEM,
		  .name   = "spi_nor_reg",
	  },
	  [2] = {
		  .start  = SPI_FC0_INT,
		  .end	  = SPI_FC0_INT,
		  .flags  = IORESOURCE_IRQ,

	  },
  };

struct platform_device zx29_device_spi_nor = {
	.name		= "spi-nor-dt",
	.id		= -1,
	.num_resources	= ARRAY_SIZE(spi_nor_resource),
	.resource	= spi_nor_resource,
};
#endif



/* --------------------------------------------------------------------
 *  I2C
 * -------------------------------------------------------------------- */
#ifdef CONFIG_I2C_ZX29

#define zx29_pmic_i2c	1
#define zx29_I2C0		1


#ifdef zx29_pmic_i2c
static struct zx29_i2c_platform_data zx29_pmic_i2c_platform_data = {
	.bus_clk_rate   = 300000,
};

static struct resource pmic_i2c_resources[] = {
	[0] = {
		.start	= (u32)ZX_PMIC_I2C_BASE,
		.end	= (u32)ZX_PMIC_I2C_BASE + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= I2C0_INT,
		.end	= I2C0_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_pmic_i2c_device = {
	.name		= "zx29_i2c",
	.id 	    = 0,
	.resource	= pmic_i2c_resources,
	.num_resources	= ARRAY_SIZE(pmic_i2c_resources),
	.dev = {
		.platform_data = &zx29_pmic_i2c_platform_data,
	},
};
#endif

#ifdef zx29_I2C0
static struct zx29_i2c_platform_data zx29_i2c0_platform_data = {
	.bus_clk_rate	 = 300000,
};

static struct resource i2c0_resources[] = {
	[0] = {
		.start	= (u32)ZX_I2C1_BASE,
		.end	= (u32)ZX_I2C1_BASE + SZ_4K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= I2C1_INT,
		.end	= I2C1_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_i2c0_device = {
	.name		= "zx29_i2c",
	.id 	    = 1,
	.resource	= i2c0_resources,
	.num_resources	= ARRAY_SIZE(i2c0_resources),
	.dev = {
		.platform_data = &zx29_i2c0_platform_data,
	},
};
#endif


#endif //end CONFIG_I2C_ZX29

/* --------------------------------------------------------------------
 *  SPI
 * -------------------------------------------------------------------- */
#ifdef CONFIG_SPI_ZX29
static struct resource spi0_resources[] = {
	[0]={
		.start	= ZX_SSP0_BASE,
		.end	= ZX_SSP0_BASE + SZ_32 - 1,
		.name	= "registers",
		.flags	= IORESOURCE_MEM,
	},
	[1]={
		.start	= SSP0_INT,
		.end	= SSP0_INT,
		.name	= "interrupt",
		.flags	= IORESOURCE_IRQ,
	},
};

static struct zx29_spi_controller spi0_data ={
	.bus_id = 0,
	.num_chipselect = 1,
	.enable_dma = 1,
	.autosuspend_delay=0,
	.dma_tx_param = (void*) DMA_CH_SSP0_TX,
	.dma_rx_param = (void*) DMA_CH_SSP0_RX,
};

static struct platform_device zx29_ssp0_device = {
	.name		= "zx29_ssp",
	.id 	= 0,
	.dev	={
			.platform_data = &spi0_data,
		},
	.resource	= spi0_resources,
	.num_resources	= ARRAY_SIZE(spi0_resources),
};
#endif

/* --------------------------------------------------------------------
 *  USB
 * -------------------------------------------------------------------- */
#ifdef CONFIG_DWC_OTG_USB
/* USB 20*/
static struct resource zx29_usb0_resources[] = {
	[0] = {
		.start	= ZX29_USB_PHYS,
		.end	= ZX29_USB_PHYS + SZ_256K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.name   = "usb_int",
		.start	= USB_INT,
		.end	= USB_INT,
		.flags	= IORESOURCE_IRQ,
	},
	[2] = {
	 	 .name   = "usb_powerdown_up",
		 .start  = USB_POWERDWN_UP_INT,
		 .end	 = USB_POWERDWN_UP_INT,
		 .flags  = IORESOURCE_IRQ,
	},
	[3] = {
	 	 .name   = "usb_powerdown_down",
		 .start  = USB_POWERDWN_DOWN_INT,
		 .end	 = USB_POWERDWN_DOWN_INT,
		 .flags  = IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_usb0_device = {
	.name		= "zx29_hsotg",
	.id		= 0,
	.resource	= zx29_usb0_resources,
	.num_resources	= ARRAY_SIZE(zx29_usb0_resources),
};
#endif

#ifdef CONFIG_USB_DWC_OTG_HCD
 /* HSIC*/
static struct resource zx29_usb1_resources[] = {
 [0] = {
	 .start  = ZX29_HSIC_PHYS,
	 .end	 = ZX29_HSIC_PHYS + SZ_256K - 1,
	 .flags  = IORESOURCE_MEM,
 },
 [1] = {
 	 .name   = "hsic_int",
	 .start  = HSIC_INT,
	 .end	 = HSIC_INT,
	 .flags  = IORESOURCE_IRQ,
 },
 [2] = {
 	 .name   = "hsic_powerdown_up",
	 .start  = HSIC_POWERDWN_UP_INT,
	 .end	 = HSIC_POWERDWN_UP_INT,
	 .flags  = IORESOURCE_IRQ,
 },
 [3] = {
 	 .name   = "hsic_powerdown_down",
	 .start  = HSIC_POWERDWN_DOWN_INT,
	 .end	 = HSIC_POWERDWN_DOWN_INT,
	 .flags  = IORESOURCE_IRQ,
 },
};

static struct platform_device zx29_usb1_device = {
 .name		 = "zx29_hsic",
 .id	 = 1,
 .resource	 = zx29_usb1_resources,
 .num_resources  = ARRAY_SIZE(zx29_usb1_resources),
};
#endif

/* --------------------------------------------------------------------
 * ICP
 * -------------------------------------------------------------------- */
#ifdef CONFIG_RPM_ZX29
 /*ICP_M0/ICP_ARM0*/
static struct zx29_rpmsg_platform_data rpmsg_m0_platform_data = {
	.iram_send_base	= (u32)ICP_IRAM_APM0_BASEADDR,
	.iram_send_size	= ICP_IRAM_APM0_SIZE,
  	.iram_recv_base	= (u32)ICP_IRAM_M0AP_BASEADDR,
	.iram_recv_size	= ICP_IRAM_M0AP_SIZE,
  	.ddr_send_base	= (u32)ICP_DDR_APM0_BASEADDR,
  	.ddr_send_size	= ICP_DDR_APM0_SIZE,
  	.ddr_recv_base	= (u32)ICP_DDR_M0AP_BASEADDR,
  	.ddr_recv_size	= ICP_DDR_M0AP_SIZE,
  	.max_channel_cnt= CHANNEL_AP2M0_MAXID,
};

static struct resource icp_m0_resources[] = {
	[0] = {
		.start	= ICP_M02PS_INT,
		.end	= ICP_M02PS_INT,
		.flags	= IORESOURCE_IRQ,
	},
	[1] = {
		.start	= (u32)ZX29_ICP_APM0_REG,
		.end	= (u32)ZX29_ICP_APM0_REG + 0x30,
		.flags	= IORESOURCE_MEM,
		.name 	= "icp",
	},

};

static struct zx29_rpmsg_platform_data rpmsg_ps_platform_data = {
	.iram_send_base	= (u32)ICP_IRAM_APPS_BASEADDR,
	.iram_send_size	= ICP_IRAM_APPS_SIZE,
  	.iram_recv_base	= (u32)ICP_IRAM_PSAP_BASEADDR,
	.iram_recv_size	= ICP_IRAM_PSAP_SIZE,
  	.ddr_send_base	= (u32)ICP_DDR_APPS_BASEADDR,
  	.ddr_send_size	= ICP_DDR_APPS_SIZE,
  	.ddr_recv_base	= (u32)ICP_DDR_PSAP_BASEADDR,
  	.ddr_recv_size	= ICP_DDR_PSAP_SIZE,
  	.max_channel_cnt= CHANNEL_AP2PS_MAXID,
};

static struct resource icp_ps_resources[] = {
	[0] = {
		.start	= ICP_AP2PS_INT,
		.end	= ICP_AP2PS_INT,
		.flags	= IORESOURCE_IRQ,
	},
	[1] = {
		.start	= (u32)ZX29_ICP_APPS_REG,
		.end	= (u32)ZX29_ICP_APPS_REG + 0x30,
		.flags	= IORESOURCE_MEM,
		.name 	= "icp",
	},
};

/* AP <--> m0 */
static struct platform_device zx29_icp_m0_device = {
	.name		= "icp",
	.id			= 0,
	.resource	= icp_m0_resources,
	.num_resources	= ARRAY_SIZE(icp_m0_resources),
	.dev = {
		.platform_data = &rpmsg_m0_platform_data,
	},
};

/* AP <--> ps */
static struct platform_device zx29_icp_ps_device = {
	.name		= "icp",
	.id			= 1,
	.resource	= icp_ps_resources,
	.num_resources	= ARRAY_SIZE(icp_ps_resources),
	.dev = {
		.platform_data = &rpmsg_ps_platform_data,
	},
};
#endif

/* --------------------------------------------------------------------
 *	WDT -- ap watchdog timer
* -------------------------------------------------------------------- */
#ifdef CONFIG_ZX29_WATCHDOG
static struct resource wdt_res[] = {
	[0] = {
		.start	= (u32)ZX_AP_WDT_BASE,
		.end	= (u32)ZX_AP_WDT_BASE + SZ_4K - 1,
		.flags  = IORESOURCE_MEM,
	},
	[1] = {
		.start = WDT_INT,
		.end   = WDT_INT,
		.flags = IORESOURCE_IRQ,
	},
};
static struct platform_device zx29_wdt_device = {
	.name = "zx29_ap_wdt",
	.id = 0,
	.resource = wdt_res,
	.num_resources	= ARRAY_SIZE(wdt_res),
};
#endif

#ifdef CONFIG_LEDS_GPIO

struct gpio_led leds[]={
#ifdef CONFIG_JCV_HW_UZ901_V1_4
	{
		.name = "modem_r_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_RED,
		.func = LED_MODEM_RED_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "modem_g_led",	
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_GREEN,
		.func = LED_MODEM_GREEN_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "wifi_led",	
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_WIFI_GREEN,
		.func = LED_WIFI_GREEN_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
#if defined(CONFIG_JCV_HW_UZ901_V1_6) || defined(CONFIG_JCV_HW_UZ901_V2_5)
	{
		.name = "sim_hotswap",
		.pin_select = 0,
		.gpio = PIN_SIM_HOTSWAP,
		.func = PIN_SIM_HOTSWAP_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
#endif
	{
		.name = "sim_switch_a_ctrl",
		.pin_select = 0,
		.gpio = PIN_SIM_SWITCH_A_CTRL,
		.func = PIN_SIM_SWITCH_A_CTRL_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
    }

#elif defined(CONFIG_JCV_HW_POWERBANK_DZ801)
	{
		.name = "modem_r_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_RED,
		.func = LED_MODEM_RED_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "modem_g_led",	
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_GREEN,
		.func = LED_MODEM_GREEN_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "wifi_g_led",	
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_WIFI_GREEN,
		.func = LED_WIFI_GREEN_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
#ifdef CONFIG_JCV_HW_DZ802_V1_0
	//DZ802
	{
		.name = "modem_b_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_BLUE,
		.func = LED_MODEM_BLUE_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
	},
	{
		.name = "sim_hotswap",
		.pin_select = 0,
		.gpio = PIN_SIM_HOTSWAP,
		.func = PIN_SIM_HOTSWAP_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
#elif defined(CONFIG_JCV_HW_DZ803_V1)
	//DZ803
	{
		.name = "wifi_r_led",	
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_BLUE,
		.func = LED_MODEM_BLUE_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
	},
	{
		.name = "sim_hotswap",
		.pin_select = 0,
		.gpio = PIN_SIM_HOTSWAP,
		.func = PIN_SIM_HOTSWAP_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "powerbank_en",
		.pin_select = 0,
		.gpio = PIN_POWERBANK_EN,
		.func = PIN_POWERBANK_EN_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
	},
#else
	//DZ801
	{
		.name = "modem_b_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_BLUE,
		.func = LED_MODEM_BLUE_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
	},
#endif
	{
		.name = "sim_switch_a_ctrl",
		.pin_select = 0,
		.gpio = PIN_SIM_SWITCH_A_CTRL,
		.func = PIN_SIM_SWITCH_A_CTRL_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
    }
#elif defined(CONFIG_JCV_HW_GS28V_V1)
	{
		.name = "modem_r_led",
		.pin_select = 0,
		.gpio = PIN_LED_MODEM_RED,
		.func = LED_MODEM_RED_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_KEEP,
	},
	{
		.name = "modem_g_led",	
		.pin_select = 0,
		.gpio = PIN_LED_MODEM_GREEN,
		.func = LED_MODEM_GREEN_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_KEEP,
	},
	{
        .name = "sim_switch_a_ctrl",
        .pin_select = 0,
        .gpio = PIN_SIM_SWITCH_A_CTRL,
        .func = PIN_SIM_SWITCH_A_CTRL_FUNC_SEL,
        .active_low = 0,
        .default_state=LEDS_GPIO_DEFSTATE_OFF,
    }
#elif defined(CONFIG_JCV_HW_MZ901_V_0)
#if !defined(CONFIG_CHARLIEPLEX_LED_DIGITS)
	{
		.name = "modem_r_led",
		.pin_select = 0,
		.gpio = PIN_LED_MODEM_RED,
		.func = LED_MODEM_RED_FUNC_SEL,
		.active_low = 1,
	},
	{
		.name = "modem_g_led",	
		.pin_select = 0,
		.gpio = PIN_LED_MODEM_GREEN,
		.func = LED_MODEM_GREEN_FUNC_SEL,
		.active_low = 1,
	},
	{
		.name = "wifi_g_led",	
		.pin_select = 0,
		.gpio = PIN_LED_WIFI_GREEN,
		.func = LED_WIFI_GREEN_FUNC_SEL,
		.active_low = 1,
	},
	{
		.name = "battery_1_led",			
		.pin_select = 0,
		.gpio = PIN_LED_BATTARY_PERCENT_25,
		.func = LED_BATTARY_PERCENT_25_FUNC_SEL,
		.active_low = 1,
		.default_state=LEDS_GPIO_DEFSTATE_OFF,
	},
	{
		.name = "battery_2_led",			
		.pin_select = 0,
		.gpio = PIN_LED_BATTARY_PERCENT_50,
		.func = LED_BATTARY_PERCENT_50_FUNC_SEL,
		.active_low = 1,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "battery_3_led",			
		.pin_select = 0,
		.gpio = PIN_LED_BATTARY_PERCENT_75,
		.func = LED_BATTARY_PERCENT_75_FUNC_SEL,
		.active_low = 1,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "battery_4_led",			
		.pin_select = 0,
		.gpio = PIN_LED_BATTARY_PERCENT_100,
		.func = LED_BATTARY_PERCENT_100_FUNC_SEL,
		.active_low = 1,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
        .name = "sim_switch_a_ctrl",
        .pin_select = 0,
        .gpio = PIN_SIM_SWITCH_A_CTRL,
        .func = PIN_SIM_SWITCH_A_CTRL_FUNC_SEL,
        .active_low = 1,
        .default_state=LEDS_GPIO_DEFSTATE_OFF,
    }
#endif
#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
	{
		.name = "modem_r_led",
		.pin_select = 0,
		.gpio = PIN_LED_MODEM_RED,
		.func = LED_MODEM_RED_FUNC_SEL,
		.active_low = 0,
	},
	{
		.name = "modem_g_led",	
		.pin_select = 0,
		.gpio = PIN_LED_MODEM_GREEN,
		.func = LED_MODEM_GREEN_FUNC_SEL,
		.active_low = 0,
	},
	{
		.name = "wifi_led",	
		.pin_select = 0,
		.gpio = PIN_LED_WIFI,
		.func = LED_WIFI_FUNC_SEL,
		.active_low = 0,
	},
	{
		.name = "wifi_g_led",	
		.pin_select = 0,
		.gpio = PIN_LED_WIFI_GREEN,
		.func = LED_WIFI_GREEN_FUNC_SEL,
		.active_low = 0,
	},
	{
		.name = "battery_r_led",			
		.pin_select = 0,
		.gpio = PIN_LED_BATTARY_RED,
		.func = LED_BATTARY_RED_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "battery_g_led",
		.pin_select = 0,
		.gpio = PIN_LED_BATTARY_GREEN,
		.func = LED_BATTARY_GREEN_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
		//.hw_timer = 1,
		//.retain_state_suspended = 1,
	},
	{
        .name = "sim_switch_a_ctrl",
        .pin_select = 0,
        .gpio = PIN_SIM_SWITCH_A_CTRL,
        .func = PIN_SIM_SWITCH_A_CTRL_FUNC_SEL,
        .active_low = 0,
        .default_state=LEDS_GPIO_DEFSTATE_OFF,
    }
#else
	#if 0
	{
		.name = "modem_r_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_RED,
		.func = LED_MODEM_RED_FUNC_SEL,
		.active_low = 0,
	},
	#ifdef CONFIG_MIN_VERSION
	{
		.name = "modem_g_led",	
			.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_GREEN,
		.func = LED_MODEM_GREEN_FUNC_SEL,
		.active_low = 0,
	},
	#else
	{
		.name = "modem_b_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_MODEM_BLUE,
		.func = LED_MODEM_BLUE_FUNC_SEL,
		.active_low = 0,
	},
	#endif
	{
		.name = "sms_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_SMS,
		.func = LED_SMS_FUNC_SEL,
		.active_low = 0,
	},
	{
		.name = "wifi_led",	
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_WIFI,
		.func = LED_WIFI_FUNC_SEL,
		.active_low = 0,
	},
	{
		.name = "battery_r_led",			
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_BATTARY_RED,
		.func = LED_BATTARY_RED_FUNC_SEL,
		.active_low = 0,
		.default_state=LEDS_GPIO_DEFSTATE_ON,
	},
	{
		.name = "battery_g_led",
		.pin_select = 0,/*gpio*/
		.gpio = PIN_LED_BATTARY_GREEN,
		.func = LED_BATTARY_GREEN_SEL,
		.active_low = 0,
		//.hw_timer = 1,
		//.retain_state_suspended = 1,
	}
	#endif	
	/*,
	{
		.name = "sink1",
		.pin_select = 1,
		//.gpio = PIN_LED_BATTARY_GREEN,
		//.func = LED_BATTARY_GREEN_SEL,
		.active_low = 0,
		//.hw_timer = 1,
		//.retain_state_suspended = 1,
	}*/
#endif
	
};

extern  int  platform_gpio_blink_set(unsigned pin_sel,unsigned gpio, int state,
			unsigned long *delay_on, unsigned long *delay_off);

static struct gpio_led_platform_data leds_data =
{
	.num_leds =sizeof(leds)/sizeof(leds[0]) ,
	.leds = leds,
	.gpio_blink_set=platform_gpio_blink_set,

};

static struct platform_device leds_device =
{
	.name		= "leds-gpio",
	.id		= 1,
	.dev		= {
		.platform_data	= &leds_data,
	},
};
#endif


#ifdef CONFIG_KEYBOARD_ZX_INT
/* --------------------------------------------------------------------
 *  Keypad (power_on, ufi, ufi_reset) for ufi
 * -------------------------------------------------------------------- */
static struct gpio_keys_button zx29_keypad_int[] = {
	#if defined(CONFIG_JCV_HW_MZ803_V3_2)
	{
		.active_low	= 1,				/*�Ƿ�͵�ƽ��Ч��1: ����Ϊ�͵�ƽ  0: ����Ϊ�ߵ�ƽ*/
		.desc       = "kpd_power",
		.code       = KEY_POWER         /* power: 116 */,
		.use_pmu_pwron = 1,             /*true: use pmu pwron interrupt fase: use zx297520v2 ext int*/
		/*
		// unnecessary for the situation of (.use_pmu_pwron = 1)
		.gpio		= PIN_KPD_POWER,
		.gpio_sel_gpio = KPD_POWER_FUNC_GPIO,
		.gpio_sel_int = KPD_POWER_FUNC_INT,
		*/
    },
	#endif

	#if defined(CONFIG_JCV_WPS_KEY_SUPPORT)
	{
		.gpio		= ZX29_GPIO_53,
		.active_low	= 1,
		.desc       = "kpd_wps",
		.code       = KEY_KPEQUAL,       /* wps: 117 */
		.gpio_sel_gpio = GPIO53_GPIO53,
		.gpio_sel_int = GPIO53_EXT_INT6,
    },
	#endif

    #if 1
	{
		.gpio		= ZX29_GPIO_52,
		.active_low	= 1,
		.desc       = "kpd_reset",
		.code       = KEY_KPPLUSMINUS,   /* reset: 118 */
		.gpio_sel_gpio = GPIO52_GPIO52,
		.gpio_sel_int = GPIO52_EXT_INT5,
    },
	#endif
};

static struct gpio_keys_platform_data zx29_keypad_int_data = {
	.buttons	= zx29_keypad_int,
	.nbuttons	= ARRAY_SIZE(zx29_keypad_int),
};

static struct platform_device zx29_keypad_int_device ={
	.name 	= 	"zx29_keypad_int",
	.id 	=	-1,
	.dev	= 	{
		.platform_data = &zx29_keypad_int_data,
	}
};
#endif

#ifdef CONFIG_KEYBOARD_ZX_5x6
static struct zx29_5x6_keypad_platform_data zx29_5x6_keypad_data = {
	.key_map = {
		{11,  12,  13,  14,  15,  16},
		{21,  22,  23,  24,  25,  26},
		{31,  32,  33,  34,  35,  36},
		{41,  42,  43,  44,  45,  46},
		{51,  52,  53,  54,  55,  56}
	},
	.pin_col_row = {83, 84, 85, 86},
};

static struct resource kpd5x6_resources[] = {
	{
		.start	= KEYPAD_INT,
		.end	= KEYPAD_INT,
		.flags	= IORESOURCE_IRQ,
	},
};

static struct platform_device zx29_5x6_keypad_device ={
	.name 	= 	"zx29_keypad",
	.id 	=	0,
	.resource		= kpd5x6_resources,
	.num_resources	= ARRAY_SIZE(kpd5x6_resources),
	.dev	= {
        .platform_data = &zx29_5x6_keypad_data,
    }
};
#endif

#ifdef CONFIG_NET_ZX29_GMAC
/* gmac*/
#if 0
static struct resource gmac_resources[] = {
	[0] = {
		.start	= ZX_GMAC_BASE,
		.end	= ZX_GMAC_BASE + SZ_8K - 1,
		.flags	= IORESOURCE_MEM,
	},
	[1] = {
		.start	= GMAC_INT,
		.end	= GMAC_INT,
		.flags	= IORESOURCE_IRQ,
	},
};
#endif
#if 1
static struct resource zx29_gmac_resources[] = {
	[0] = {
		.start	= ZX_GMAC_BASE,
		.end	= ZX_GMAC_BASE + SZ_8K - 1,
		.flags	= IORESOURCE_MEM,
	},
#if 0
	[1] = {
		.name   = "gmac_int",
		.start	= GMAC_INT,
		.end	= GMAC_INT,
		.flags	= IORESOURCE_IRQ,
	},
	[2] = {
		.name   = "phy_int",
		.start	= GMACPHY_INT,
		.end	= GMACPHY_INT,
		.flags	= IORESOURCE_IRQ,
	},
#endif
};
#endif
static struct platform_device zx29_gmac_device = {
	.name		= "zx29_gmac",
	.id		= 0,
	.resource	= zx29_gmac_resources,
	.num_resources	= ARRAY_SIZE(zx29_gmac_resources),
	.dev	 = {
			.platform_data = NULL,
		    }
};
#endif

#ifdef CONFIG_CHARGER_AW3215
static struct  aw3215_platform_data aw3215_charger_platform = {
#if defined(CONFIG_JCV_HW_POWERBANK_DZ801) || defined(CONFIG_JCV_HW_GS28V_V1)
	.gpio_chgen = ZX29_GPIO_128,
	.gpio_chgen_gpio_sel = GPIO128_GPIO128,
	.gpio_chgin = ZX29_GPIO_48,
	.gpio_chgin_fun_sel = GPIO48_EXT_INT1,
	.gpio_chgin_gpio_sel =GPIO48_GPIO48,
	.gpio_chgctrl = ZX29_GPIO_0,/*not used*/
	.gpio_chgctrl_gpio_sel = GPIO0_GPIO0,/*not used*/
	//MZ801 IP5306H without chagstate,  use for chgoutdet
	.gpio_chgstate = ZX29_GPIO_50,
	.gpio_chgstate_fun_sel = GPIO50_EXT_INT3,
	.gpio_chgstate_gpio_sel = GPIO50_GPIO50,
#elif defined(CONFIG_JCV_HW_MZ901_V_0)
	.gpio_chgen = ZX29_GPIO_128,
	.gpio_chgen_gpio_sel = GPIO128_GPIO128,
	.gpio_chgin = ZX29_GPIO_48,
	.gpio_chgin_fun_sel = GPIO48_EXT_INT1,
	.gpio_chgin_gpio_sel =GPIO48_GPIO48,
	.gpio_chgctrl = ZX29_GPIO_130,/*not used*/
	.gpio_chgctrl_gpio_sel = GPIO130_GPIO130,/*not used*/
	.gpio_chgstate = ZX29_GPIO_50,
	.gpio_chgstate_fun_sel = GPIO50_EXT_INT3,
	.gpio_chgstate_gpio_sel = GPIO50_GPIO50,
#else
	.gpio_chgen = ZX29_GPIO_131,
	.gpio_chgen_gpio_sel = GPIO131_GPIO131,
	.gpio_chgin = ZX29_GPIO_48,
	.gpio_chgin_fun_sel = GPIO48_EXT_INT1,
	.gpio_chgin_gpio_sel =GPIO48_GPIO48 ,
	.gpio_chgctrl = ZX29_GPIO_130,/*not used*/
	.gpio_chgctrl_gpio_sel = GPIO130_GPIO130,/*not used*/
	.gpio_chgstate = ZX29_GPIO_50,
	.gpio_chgstate_fun_sel = GPIO50_EXT_INT3,
	.gpio_chgstate_gpio_sel = GPIO50_GPIO50,
#endif
};

static struct platform_device zx29_charger_device = {
	.name		= "aw3215-charger",
	.id		= 0,
	.dev	 = {
			.platform_data = &aw3215_charger_platform,
		    }
};


#endif

/* --------------------------------------------------------------------
 *	----------  for  solution integration department ---------   end
* -------------------------------------------------------------------- */


/*
 *  device tab used by board_init()
 */
struct platform_device *zx29_device_table[] __initdata={
/* --------------------------------------------------------------------
 *	----------  for  solution integration department ---------   start
* -------------------------------------------------------------------- */
#ifdef CONFIG_SERIAL_ZX29_UART
	//&zx29_uart0_device,
	&zx29_uart1_device,
	//&zx29_uart2_device,
#endif
#ifdef CONFIG_MTD_NAND_DENALI
	&zx29_device_nand,
#endif
#ifdef CONFIG_DWC_OTG_USB
	&zx29_usb0_device,
#endif
#ifdef CONFIG_USB_DWC_OTG_HCD
	&zx29_usb1_device,
#endif
#ifdef CONFIG_MTD_ZXIC_SPIFC
	&zx29_device_spi_nand,
#endif

#ifdef CONFIG_SPI_ZXIC_NOR
	&zx29_device_spi_nor,
#endif


#ifdef CONFIG_ZX29_DMA
	&zx29_dma_device,
#endif

#ifdef CONFIG_MMC_ZX29
#ifdef CONFIG_XR_WLAN
    &zx29_sdmmc1_device,
#else
    &zx29_sdmmc0_device,
    &zx29_sdmmc1_device,
#endif
#endif

#ifdef CONFIG_I2C_ZX29

#ifdef zx29_pmic_i2c
	&zx29_pmic_i2c_device,
#endif

#ifdef zx29_I2C0
	&zx29_i2c0_device,
#endif

#endif

#ifdef CONFIG_SPI_ZX29
	&zx29_ssp0_device,
#endif

#ifdef CONFIG_RPM_ZX29
	&zx29_icp_m0_device,
	&zx29_icp_ps_device,
#endif

#ifdef CONFIG_ZX29_WATCHDOG
	&zx29_wdt_device,
#endif

#ifdef CONFIG_KEYBOARD_ZX_5x6
	&zx29_5x6_keypad_device,
#endif

#ifdef CONFIG_KEYBOARD_ZX_INT
    &zx29_keypad_int_device,
#endif

#ifdef CONFIG_CHARGER_AW3215
       &zx29_charger_device,
#endif

#ifdef CONFIG_LEDS_GPIO
       &leds_device,
#endif

#ifdef CONFIG_NET_ZX29_GMAC
 	&zx29_gmac_device,
#endif

#if (defined CONFIG_SND_SOC_ZX_I2S) || (defined CONFIG_SND_SOC_ZX_I2S_MODULE)
#ifdef zx29_I2S0
	&zx29_i2s0_device,
#endif
#ifdef zx29_I2S1
	&zx29_i2s1_device,
#endif
#endif

#if (defined CONFIG_SND_SOC_ZX_VOICE) || (defined CONFIG_SND_SOC_ZX_VOICE_MODULE)
	&voice_asoc_device,
#endif

#if (defined CONFIG_SND_SOC_ZX_PCM) || (defined CONFIG_SND_SOC_ZX_PCM_MODULE)
	&zx29_asoc_dma,
#endif
#if (defined CONFIG_SND_SOC_ZX297520V3) || (defined CONFIG_SND_SOC_ZX297520V3_MODULE)
	&zx29_audio,
#endif
};

unsigned int zx29_device_table_num=ARRAY_SIZE(zx29_device_table);

#if (defined CONFIG_SPI_ZX29) || (defined CONFIG_SPI_GPIO)
struct zx29_lcd_platform_data lead_lcd_platform = {
	.spi_dcx_gpio = PIN_SPI_DCX,
	.spi_dcx_gpio_fun_sel = SPI_DCX_FUNC_SEL,
	.lcd_blg_gpio = PIN_LCD_BLG,
	.lcd_blg_gpio_fun_sel = LCD_BLG_FUNC_SEL,
	.lcd_rst_gpio = PIN_LCD_RST,
	.lcd_rst_gpio_fun_sel =LCD_RST_FUNC_SEL,
};

static const struct spi_config_chip lead_lcd_chip_info = {
	.com_mode = DMA_TRANSFER,
	.iface = SPI_INTERFACE_MOTOROLA_SPI,
	.hierarchy = SPI_MASTER,
	.slave_tx_disable = 1,//DO_NOT_DRIVE_TX
	.rx_lev_trig = SPI_RX_4_OR_MORE_ELEM,
	.tx_lev_trig = SPI_TX_4_OR_MORE_EMPTY_LOC,
//	.ctrl_len = SSP_BITS_8,
//	.wait_state = SSP_MWIRE_WAIT_ZERO,
//	.duplex = SSP_MICROWIRE_CHANNEL_FULL_DUPLEX,
//	.cs_control = null_cs_control,
};
static struct spi_board_info zx29_spi_devices[] = {
#ifdef CONFIG_FB_LEADT15DS26
    {
        .modalias 	    = "lead_t15ds26",
        .bus_num 	    = 0,
        .chip_select 	= 0,
        .max_speed_hz	= 13000000,
        .mode		    = SPI_MODE_3,
        .platform_data 	= &lead_lcd_platform,
        .controller_data = &lead_lcd_chip_info,
    },
#endif
};
void __init spi_add_devices(void)
{
	unsigned  devices_num = ARRAY_SIZE(zx29_spi_devices);
    int ret = 0;
	printk("spi_register_board_info success,devices_num=%d\n",devices_num);
	if (devices_num){
		ret = spi_register_board_info(zx29_spi_devices, devices_num);
		printk("spi_register_board_info success,ret=%d\n",ret);
		if(ret)
			BUG();
	}
}
#endif

#ifdef CONFIG_CHARGER_ZX234502

#define ZX234502_BAT_VOLTAGE_LEN  21

struct zx234502_bat_calibration zx234502_bat_volage_charge[]=
{
	{4100,100}, {4090,95}, {4080,90}, {4070,85}, {4060,80}, {4050,75},
	{4012,70}, {3973,65}, {3935,60}, {3896,55}, {3860,50}, {3817,45},
	{3775,40}, {3733,35}, {3692,30}, {3650,25}, {3610,20}, {3570,15},
	{3530,12}, {3590,10}, {3450,5}
};

struct zx234502_bat_calibration zx234502_bat_volage_discharge[]=
{
	{4100,100}, {4090,95}, {4080,90}, {4070,85}, {4060,80}, {4050,75},
	{4012,70}, {3973,65}, {3935,60}, {3896,55}, {3860,50}, {3817,45},
	{3775,40}, {3733,35}, {3692,30}, {3650,25}, {3610,20}, {3570,15},
	{3530,12}, {3590,10}, {3450,5}
};

static struct  zx234502_platform_data zx234502_charger_platform = {
	.gpio_int		=	PIN_CHARGE_INT,  //gpio55
	.gpio_int_fun_sel = CHARGE_INT_FUNC_SEL,
	.charging         =  &zx234502_bat_volage_charge,
	.charging_size = ZX234502_BAT_VOLTAGE_LEN,
	.discharging      =  &zx234502_bat_volage_discharge,
	.discharging_size = ZX234502_BAT_VOLTAGE_LEN,
	.ts_flag = TRUE,
	.boost_flag = FALSE,
	.boost_cur_gpio1 = PIN_CHARGE_BOOST_GPIO1,/*GPIO39*/
	.boost_gpio1_fun_sel = CHARGE_BOOST_GPIO1_FUNC_SEL,
	.boost_cur_gpio2 = PIN_CHARGE_BOOST_GPIO2,/*GPIO40*/
	.boost_gpio2_fun_sel = CHARGE_BOOST_GPIO2_FUNC_SEL,
	.boost_cur_gpio3 = PIN_CHARGE_BOOST_GPIO3,/*GPIO41*/
	.boost_gpio3_fun_sel = CHARGE_BOOST_GPIO3_FUNC_SEL,
	.boost_loadswitch_gpio = PIN_CHARGE_BOOST_LOADSWITCH,/*GPIO38*/
	.boost_loadswitch_fun_sel = CHARGE_BOOST_LOADSWITCH_FUNC_SEL,
};

#endif
#ifdef CONFIG_TSC_ZX29
u32 ts_temp_value_table[TS_ADC_TEMP_NUMBER][TS_ADC_TEMP_VOLTAGE_NUMBER]={
{30,31,32,33,34,35,36,37,38,39,
 40,41,42,43,44,45,46,47,48,49,
 50,51,52,53,54,55,56,57,58,59,
 60,61,62,63,64,65,66,67,68,69,
 70,71,72,73,74,75,76,77,78,79,
 80,81,82,83,84,85,86,87,88,89,
 90,91,92,93,94,95,96,97,98,99,
 100,101,102,103,104,105,106,107,108,109,
 110,111,112,113,114,115,116,117,118,119,
 120,121,122,123,124,125},

{
 1422,1408,1395,1381,1367,1353,1338,1323,1308,1293,
 1278,1262,1247,1231,1215,1199,1183,1166,1149,1133,
 1116,1100,1083,1066,1049,1032,1015,998,981,965,
 948,931,915,898,882,865,849,833,816,801,
 785,769,754,739,723,708,694,679,665,650,
 636,623,610,596,583,570,558,545,532,521,
 509,498,486,475,464,454,443,432,423,412,
 402,394,384,375,367,358,350,341,333,326,
 317,310,302,295,289,282,275,268,262,256,
 250,242,239,233,227,222 }
};
volatile u32 ts_adc_flag=2;// 2:adc2,others:adc1
#endif
#if 1
/*
 *  I2C  device tab used by board_init()
 */
#ifdef CONFIG_MFD_ZX234290_I2C
static struct  zx234290_board zx234290_platform = {
	.irq_gpio_num	    =	PIN_PMU_INT, //EX0_INT,
    .irq_gpio_func      =   PMU_INT_FUNC_SEL,
	.pshold_gpio_num    =   PIN_PMU_PSHOLD,
	.pshold_gpio_func   =   PMU_PSHOLD_FUNC_SEL,
	.irq_base	= 	ENT_ZX234290_IRQ_BASE,
};
#endif

static struct i2c_board_info zx29_i2c0_devices[] = {
#ifdef CONFIG_MFD_ZX234290_I2C
	[0]={
		I2C_BOARD_INFO("zx234290", 0x12),
		.irq		= EX0_INT,
		.platform_data 	= &zx234290_platform,
	},
#endif

};

static struct i2c_board_info zx29_i2c1_devices[] = {
#ifdef CONFIG_CHARGER_ZX234502
		{
			I2C_BOARD_INFO("zx234502-charger", 0x13),
			//.irq		= EX5_INT,
			.platform_data	= &zx234502_charger_platform,
		},
#endif
#if (defined CONFIG_SND_SOC_ZX297520V3) || (defined CONFIG_SND_SOC_ZX297520V3_MODULE)
		{
			I2C_BOARD_INFO(CODEC_NAME, CODEC_ADDR),
			.platform_data 	= ZX29_SND_CODEC_PDATA, //&snd_codec_pdata,
		},
#endif
#ifdef CONFIG_CAMERA_DRV
		{
			I2C_BOARD_INFO("gc6133-sensor", 0x40),
		},
#endif
#ifdef CONFIG_INPUT_TOUCHSCREEN
		{
			I2C_BOARD_INFO("touchscreen", 0x38),
		},
#endif

};

void __init i2c_add_devices(void)
{
	unsigned  devices_num = 0;
    int ret = 0;

	/*
	  *i2c devices on bus 0
	  */
	devices_num = ARRAY_SIZE(zx29_i2c0_devices);
	if (devices_num){
		ret = i2c_register_board_info(0,zx29_i2c0_devices, devices_num);
		if(ret)
			BUG();
	}

	/*
	  *i2c devices on bus 1
	  */
	devices_num = ARRAY_SIZE(zx29_i2c1_devices);
	if (devices_num){
		ret = i2c_register_board_info(1,zx29_i2c1_devices, devices_num);
		if(ret)
			BUG();
	}
}
#endif
