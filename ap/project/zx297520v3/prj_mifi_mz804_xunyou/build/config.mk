############################################################################
#
# Makefile -- Top level linux makefile.
#
############################################################################

export CONFIG_WIFI_MODULE=aic8800
export CONFIG_WIFI_FUNCTION=apsta
#just for realtek
export CONFIG_WIFI_SINGLEAP=no

export CONFIG_SIGN_IMAGE=yes
export CONFIG_USE_WEBUI=yes
#export CONFIG_USE_WEBUI_ZIP=yes
export CONFIG_MMI_LCD=no

export THUMB := yes
export USE_CPPS_KO := no
export MODEM_TYPE := min

export PRJ_IS_MIN := yes

#rootfs type:ubifs jffs2 squashfs
export ROOT_FS_TYPE=squashfs

#squashfs block size in KB, only for squashfs
export SQUASHFS_BLOCK_KB=256

#userdata type:ubifs jffs2
export USERDATA_FS_TYPE=jffs2

export LARGEFILE_SPLIT_ENABLE := yes
export SPLIT_BLOCK_SIZE := 256K
#lzma compress dictionary size in KB
export LZMA_DICT=512

#libstdc++.so.6 for gps app
export USE_LIBSTDCPP := no

#yes is ttf font, other value is bitmap font
export USE_TTF_FONT = no

# pagesize 2K and block size 128K default
export PAGESIZE=0x800
export ERASEBLOCK=0x8000

# yes is debug mode,other value is release mode.
export GLOBAL_DEBUG=no

# custom macro for lib and app
CUSTOM_MACRO += -DAPP_OS_LINUX=1
CUSTOM_MACRO += -DAPP_OS_TYPE=APP_OS_LINUX
CUSTOM_MACRO += -DFOTA_RB_DL

CUSTOM_MACRO += -DPRODUCT_MIFI_CPE=0
CUSTOM_MACRO += -DPRODUCT_PHONE=1
CUSTOM_MACRO += -DPRODUCT_DATACARD=2
CUSTOM_MACRO += -DPRODUCT_TYPE=PRODUCT_MIFI_CPE

ifeq ($(CONFIG_MMI_LCD),no)
CUSTOM_MACRO += -DDISABLE_LCD
endif

ifeq ($(USE_TTF_FONT),yes)
CUSTOM_MACRO += -DENABLE_TTF_FONT
endif

CUSTOM_MACRO += -DHAVE_MODEM_IN_CORE
#CUSTOM_MACRO += -D_USE_BL
#CUSTOM_MACRO += -D_USE_VOLTE
CUSTOM_MACRO += -D_USE_CODEC_TI3100
# fota�������������Ƿ���Ҫ�û�ȷ�Ϻ�����������
export ENABLE_FOTA_UPG_USR_CONFIRM=yes

# fota�������������Ƿ���Ҫ��AT CTRL����
export ENABLE_FOTA_AT_MSG=yes

# fota���ڼ��RTC��ʱ���ÿ���
export FOTA_POLLING_USE_RTC=no

#fota���ؿ�ʹ�ù���(gs)\ redbend (rb)
export ENABLE_FOTA_DM_LIB=gs

#enable new call module
export ENABLE_NEW_CC=no
ifeq ($(ENABLE_NEW_CC),yes)
CUSTOM_MACRO += -DUSE_NEW_CC
endif
#����ATCTL��phoneĿ¼�еĴ����Ƿ�������
export ENABLE_PHONECODE_IN_ATCTL=yes

#�����Ƿ�ʹ������������ͷ
export USE_VOICE_SUPPORT := no
#control delete other fota_dm file
export CONFIG_USER_SINGLE_DM=gs

#�����Ƿ�ʹ�û���
export USE_MIXDATA_SUPPORT=no

export USE_L1G := no
export USE_RAT_TDS := no
export USE_FOTA := no
CUSTOM_MACRO += -DFOTA_DISABLE=1

export USE_REMOVE_COMMENT := yes
CUSTOM_MACRO += -DPRODUCT_NOT_USE_RTC

# y for yes, n for no, keep same format with kernel configuration
export CONFIG_MIN_8M_VERSION := y
CUSTOM_MACRO += -DCONFIG_MIN_8M_VERSION=y

#�ļ���ǩ����
export VERIFY_APP_IN_KERNEL := yes

# QRZL_UE use control all add by svk@20250317
export QRZL_UE := yes
CUSTOM_MACRO += -DQRZL_UE

# zmr QRZL APP 
export ENABLE_QRZL_APP := yes
CUSTOM_MACRO += -DENABLE_QRZL_APP
CUSTOM_MACRO += -DQRZL_APP_CUSTOMIZATION_XY_LED  # 读不到卡红灯闪烁，读到卡红灯常亮，有网络后，网络灯总是亮绿灯 (默认情况下，有网络后，网络灯会闪烁)
CUSTOM_MACRO += -DQRZL_WIFI_CONNECTED_ALWAYS_GREEN
CUSTOM_MACRO += -DQRZL_KEY_NO_WAKEUP_WIFI
CUSTOM_MACRO += -DQRZL_NET_DISABLE_NO_CHANGE_LED
CUSTOM_MACRO += -DQRZL_ESIM2_ON_SIM_SLOT
CUSTOM_MACRO += -DQRZL_DEVICE_CONTROL_ENABLE
CUSTOM_MACRO += -DQRZL_DEVICE_RESTORE_PUSH   # 恢复出厂设置前上报一次
# 电信认证
CUSTOM_MACRO += -DQRZL_CMP_AUTH
# 移动认证
CUSTOM_MACRO += -DQRZL_ONE_LINK_AUTH


# add by svk@20241122
# for sim switch
export JCV_HW_MZ803_V3_2 := yes
CUSTOM_MACRO += -DJCV_FEATURE_ESIM_RSIM_SWITCH
CUSTOM_MACRO += -DJCV_HW_MZ803_V3_2
# for use differentiate dz801 with mz804
CUSTOM_MACRO += -DJCV_HW_MZ804_V1_4
CUSTOM_MACRO += -DJCV_FEATURE_ZVERSION_USERNV_DISABLE
# auth
export JCV_FEATURE_CAPTIVE_PORTAL_SERVER := yes
CUSTOM_MACRO += -DJCV_FEATURE_CAPTIVE_PORTAL_SERVER
CUSTOM_MACRO += -DJCV_FEATURE_WIFI_RECONNECT_PORTAL
CUSTOM_MACRO += -DCONFIG_DNS_REDIRECT
# end by svk


########################qrzl_app 优化编译大小方便OTA 升级#############################
# qrzl_app.c 中流程对应原来nv qrzl_cloud_protocol_type
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_HTTP
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_CS_HTTP
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_MQTT
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_XLX
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_HMM_MQTT
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_QC_HTTP_POST
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_QC_TCP
# export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_JIJIA_HTTP
export QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_XUNYOU_MQTT

CUSTOM_MACRO += -D$(QRZL_CLOUD_PROTOCOL)

# 当QRZL_CLOUD_PROTOCOL 选择 QRZL_CLOUD_HTTP 时，请选择以下请求类型
# qrzl_mqtt_control_client.c 选择请求类型 对应原来nv qrzl_cloud_http_request_type
# Available types: QICHENG, XUNJI, MY, LD, JIULING, XINPU, XUNYOU, BOMING
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := QICHENG
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := XUNJI
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := MY
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := LD
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := JIULING
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := XINPU
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := XUNYOU
# export QRZL_CLOUD_HTTP_REQUEST_TYPE := BOMING

# 当QRZL_CLOUD_PROTOCOL 选择 QRZL_CLOUD_MQTT 时，请选择以下请求类型
# qrzl_http_control_client.c 选择请求类型 对应原来nv qrzl_cloud_mqtt_type
# QRZL Cloud MQTT Type Control (under QRZL_CLOUD_PROTOCOL)
# Available types: MQTT_TYPE_1, KY, MQTT_YIMING, MQTT_WUXING
# export QRZL_CLOUD_MQTT_TYPE := MQTT_TYPE_1
# export QRZL_CLOUD_MQTT_TYPE := MQTT_YIMING
# export QRZL_CLOUD_MQTT_TYPE := KY
# export QRZL_CLOUD_MQTT_TYPE := MQTT_WUXING

# Add HTTP request type macro when HTTP protocol is selected
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
CUSTOM_MACRO += -DQRZL_CLOUD_HTTP_REQUEST_TYPE_$(QRZL_CLOUD_HTTP_REQUEST_TYPE)
endif

# Add individual HTTP client type macros for conditional compilation
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),QICHENG)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_QICHENG
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),XUNJI)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_XUNJI
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),MY)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_XUNJI
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),LD)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_XUNJI
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),JIULING)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_JIULING
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),XINPU)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_XINPU
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),XUNYOU)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_XINPU
endif
ifeq ($(QRZL_CLOUD_HTTP_REQUEST_TYPE),BOMING)
CUSTOM_MACRO += -DQRZL_HTTP_CLIENT_BOMING
endif

# Add MQTT type macro when MQTT protocol is selected
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
CUSTOM_MACRO += -DQRZL_CLOUD_MQTT_TYPE_$(QRZL_CLOUD_MQTT_TYPE)
endif

# Add individual MQTT client type macros for conditional compilation
ifeq ($(QRZL_CLOUD_MQTT_TYPE),MQTT_TYPE_1)
CUSTOM_MACRO += -DQRZL_MQTT_CLIENT_TYPE_1
endif
ifeq ($(QRZL_CLOUD_MQTT_TYPE),KY)
CUSTOM_MACRO += -DQRZL_MQTT_CLIENT_KY
endif
ifeq ($(QRZL_CLOUD_MQTT_TYPE),MQTT_YIMING)
CUSTOM_MACRO += -DQRZL_MQTT_CLIENT_YIMING
endif
ifeq ($(QRZL_CLOUD_MQTT_TYPE),MQTT_WUXING)
CUSTOM_MACRO += -DQRZL_MQTT_CLIENT_WUXING
endif

# ONE_LINK 支持 
# QRZL_AUTH_ONE_LINK_HTTP 移动二次认证 
# QRZL_AUTH_CMP_HTTP 电信二次认证
export QRZL_AUTH_ONE_LINK_HTTP := yes
export QRZL_AUTH_CMP_HTTP := yes
ifeq ($(QRZL_AUTH_ONE_LINK_HTTP),yes)
CUSTOM_MACRO += -DQRZL_AUTH_ONE_LINK_HTTP
endif
ifeq ($(QRZL_AUTH_CMP_HTTP),yes)
CUSTOM_MACRO += -DQRZL_AUTH_CMP_HTTP
endif

# 移动二次认证客户定制 DEFAULT移动api 走我们设备内置页面 不是默认则客户定制url页面
# For ONE_LINK Authentication  
# export QRZL_ONE_LINK_CUSTOMER_TYPE := WUXING
# export QRZL_ONE_LINK_CUSTOMER_TYPE := JIUYAO
# export QRZL_ONE_LINK_CUSTOMER_TYPE := BEIWEI
# export QRZL_ONE_LINK_CUSTOMER_TYPE := MY
# export QRZL_ONE_LINK_CUSTOMER_TYPE := CHUANGSAN
export QRZL_ONE_LINK_CUSTOMER_TYPE := DEFAULT

# 电信二次认证客户定制 DEFAULT 电信官方url，不是默认则客户定制url页面
# Customer Type Control - Select customer implementation
# For CMP Authentication
# export QRZL_CMP_CUSTOMER_TYPE := WUXING
# export QRZL_CMP_CUSTOMER_TYPE := JIUYAO
# export QRZL_CMP_CUSTOMER_TYPE := BEIWEI
# export QRZL_CMP_CUSTOMER_TYPE := MY
# export QRZL_CMP_CUSTOMER_TYPE := CHUANGSAN
export QRZL_CMP_CUSTOMER_TYPE := DEFAULT

# Add customer type macros
ifeq ($(QRZL_CMP_CUSTOMER_TYPE),WUXING)
CUSTOM_MACRO += -DQRZL_CMP_CUSTOMER_WUXING
endif
ifeq ($(QRZL_CMP_CUSTOMER_TYPE),MY)
CUSTOM_MACRO += -DQRZL_CMP_CUSTOMER_MY
endif
ifeq ($(QRZL_CMP_CUSTOMER_TYPE),JIUYAO)
CUSTOM_MACRO += -DQRZL_CMP_CUSTOMER_JIUYAO
endif
ifeq ($(QRZL_CMP_CUSTOMER_TYPE),BEIWEI)
CUSTOM_MACRO += -DQRZL_CMP_CUSTOMER_BEIWEI
endif
ifeq ($(QRZL_CMP_CUSTOMER_TYPE),CHUANGSAN)
CUSTOM_MACRO += -DQRZL_CMP_CUSTOMER_CHUANGSAN
endif

ifeq ($(QRZL_ONE_LINK_CUSTOMER_TYPE),WUXING)
CUSTOM_MACRO += -DQRZL_ONE_LINK_CUSTOMER_WUXING
endif
ifeq ($(QRZL_ONE_LINK_CUSTOMER_TYPE),MY)
CUSTOM_MACRO += -DQRZL_ONE_LINK_CUSTOMER_MY
endif
ifeq ($(QRZL_ONE_LINK_CUSTOMER_TYPE),JIUYAO)
CUSTOM_MACRO += -DQRZL_ONE_LINK_CUSTOMER_JIUYAO
endif
ifeq ($(QRZL_ONE_LINK_CUSTOMER_TYPE),BEIWEI)
CUSTOM_MACRO += -DQRZL_ONE_LINK_CUSTOMER_BEIWEI
endif
ifeq ($(QRZL_ONE_LINK_CUSTOMER_TYPE),CHUANGSAN)
CUSTOM_MACRO += -DQRZL_ONE_LINK_CUSTOMER_CHUANGSAN
endif

# 客户请求token 定制化 
# Auth Control Customer Selection - Enable specific customer auth modules
# export QRZL_AUTH_CUSTOMER := CHUANGSAN
# export QRZL_AUTH_CUSTOMER := WUXING
export QRZL_AUTH_CUSTOMER := XUNYOU
# export QRZL_AUTH_CUSTOMER := JIUYAO
# export QRZL_AUTH_CUSTOMER := JNZY
# export QRZL_AUTH_CUSTOMER := BEIWEI
# export QRZL_AUTH_CUSTOMER := SHAYIN
# export QRZL_AUTH_CUSTOMER := MY
# export QRZL_AUTH_CUSTOMER := CMP_ORIGINAL
# export QRZL_AUTH_CUSTOMER := ALL

# Add auth control customer macros
ifeq ($(QRZL_AUTH_CUSTOMER),CHUANGSAN)
CUSTOM_MACRO += -DQRZL_AUTH_CHUANGSAN
endif
ifeq ($(QRZL_AUTH_CUSTOMER),WUXING)
CUSTOM_MACRO += -DQRZL_AUTH_WUXING
endif
ifeq ($(QRZL_AUTH_CUSTOMER),XUNYOU)
CUSTOM_MACRO += -DQRZL_AUTH_XUNYOU
endif
ifeq ($(QRZL_AUTH_CUSTOMER),JIUYAO)
CUSTOM_MACRO += -DQRZL_AUTH_JIUYAO
endif
ifeq ($(QRZL_AUTH_CUSTOMER),JNZY)
CUSTOM_MACRO += -DQRZL_AUTH_JNZY
endif
ifeq ($(QRZL_AUTH_CUSTOMER),BEIWEI)
CUSTOM_MACRO += -DQRZL_AUTH_BEIWEI
endif
ifeq ($(QRZL_AUTH_CUSTOMER),SHAYIN)
CUSTOM_MACRO += -DQRZL_AUTH_SHAYIN
endif
ifeq ($(QRZL_AUTH_CUSTOMER),MY)
CUSTOM_MACRO += -DQRZL_AUTH_MY
endif
ifeq ($(QRZL_AUTH_CUSTOMER),CMP_ORIGINAL)
CUSTOM_MACRO += -DQRZL_AUTH_CMP_ORIGINAL
endif
ifeq ($(QRZL_AUTH_CUSTOMER),ALL)
CUSTOM_MACRO += -DQRZL_AUTH_ALL
endif
################################################################################
