apn_auto_config=CMCC($)cmnet($)manual($)*99#($)pap($)($)($)IP($)auto($)($)auto($)($)
APN_config0=Default($)Default($)manual($)($)($)($)($)IP($)auto($)($)auto($)($)
APN_config1=
APN_config2=
APN_config3=
APN_config4=
APN_config5=
APN_config6=
APN_config7=
APN_config8=
APN_config9=
apn_index=0
apn_mode=auto
at_snap_flag=3
at_wifi_mac=0
auto_apn_index=0
cid_reserved=0
clear_pb_when_restore=no
clear_sms_when_restore=no
default_apn=3gnet
ipv6_APN_config1=
ipv6_APN_config2=
ipv6_APN_config3=
ipv6_APN_config4=
ipv6_APN_config5=
ipv6_APN_config6=
ipv6_APN_config7=
ipv6_APN_config8=
ipv6_APN_config9=
m_profile_name=Internux
need_init_modem=yes
net_select=NETWORK_auto
pdp_type=IPv4v6
ppp_apn=
max_reconnect_time=3000000
pppd_auth=noauth
ppp_auth_mode=none
ppp_passwd=
ppp_pdp_type=
ppp_username=
ipv6_ppp_auth_mode=none
ipv6_ppp_passwd=
ipv6_ppp_username=
pre_mode=
prefer_dns_manual=0.0.0.0
standby_dns_manual=0.0.0.0
wan_apn=internet
wan_dial=
cta_test=0
safecare_enbale=0
safecare_hostname=mob.3gcare.cn
safecare_registed_imei=
safecare_registed_iccid=
safecare_contimestart1=
safecare_contimestart2=
safecare_contimestart3=
safecare_contimestop1=
safecare_contimestop2=
safecare_contimestop3=
safecare_contimeinterval=
safecare_mobsite=http://mob.3gcare.cn
safecare_chatsite=
safecare_platno=
safecare_mobilenumber=
safecare_version=
ethwan_dns_mode=auto
pswan_dns_mode=auto
wifiwan_dns_mode=auto
ethwan_ipv6_dns_mode=auto
wifiwan_ipv6_dns_mode=auto
pswan_ipv6_dns_mode=auto
admin_Password=admin
psw_changed=1
alg_ftp_enable=0
alg_sip_enable=0
blc_wan_auto_mode=AUTO_PPP
blc_wan_mode=AUTO
br_ipchange_flag=
br_node=usblan0
clat_fake_subnet=***********
clat_frag_collect_timeout=300
clat_local_mapping_timeout=300
clat_mapping_record_timeout=3000
clat_query_server_port=1464
DefaultFirewallPolicy=0
dev_coexist=0
dhcpDns=*************
dhcpEnabled=1
dhcpEnd=***************
dhcpLease_hour=24
dhcpStart=*************00
dhcpv6stateEnabled=0
dhcpv6statelessEnabled=1
dhcpv6statePdEnabled=0
dial_mode=auto_dial
DMZEnable=0
DMZIPAddress=
dns_extern=
ipv6_dns_extern=
eth_act_type=
eth_type=wan
ethlan=
ethwan=
ethwan_dialmode=auto
ethwan_mode=auto
ethwan_priority=3
fast_usb=usblan0
fastnat_level=2
IPPortFilterEnable=0
IPPortFilterRules_0=
IPPortFilterRules_1=
IPPortFilterRules_2=
IPPortFilterRules_3=
IPPortFilterRules_4=
IPPortFilterRules_5=
IPPortFilterRules_6=
IPPortFilterRules_7=
IPPortFilterRules_8=
IPPortFilterRules_9=
IPPortFilterRulesv6_0=
IPPortFilterRulesv6_1=
IPPortFilterRulesv6_2=
IPPortFilterRulesv6_3=
IPPortFilterRulesv6_4=
IPPortFilterRulesv6_5=
IPPortFilterRulesv6_6=
IPPortFilterRulesv6_7=
IPPortFilterRulesv6_8=
IPPortFilterRulesv6_9=
ipv4_fake_subnet=*********
ipv6_fake_subnet=2016::1
lan_ipaddr=*************
lan_name=br0
lan_netmask=*************
LanEnable=1
mac_ip_list=
mgmt_quicken_power_on=0
mtu=1400
natenable=
dosenable=0
need_jilian=1
nofast_port=21+22+23+25+53+67+68+69+110+115+123+443+500+1352+1723+1990+1991+1992+1993+1994+1995+1996+1997+1998+4500+5060
nv_save_interval=300
path_conf=/etc_rw
path_ro=/etc_ro
path_log=/var/log/
path_sh=/sbin
path_tmp=/tmp
permit_gw=
permit_ip6=
permit_nm=*************
PortForwardEnable=0
PortForwardRules_0=
PortForwardRules_1=
PortForwardRules_2=
PortForwardRules_3=
PortForwardRules_4=
PortForwardRules_5=
PortForwardRules_6=
PortForwardRules_7=
PortForwardRules_8=
PortForwardRules_9=
PortMapEnable=0
PortMapRules_0=
PortMapRules_1=
PortMapRules_2=
PortMapRules_3=
PortMapRules_4=
PortMapRules_5=
PortMapRules_6=
PortMapRules_7=
PortMapRules_8=
PortMapRules_9=
ppp_name=ppp0
pppoe_password=
pppoe_username=
ps_ext1=usblan0
ps_ext2=usblan0
ps_ext3=usblan0
ps_ext4=usblan0
ps_ext5=usblan0
ps_ext6=usblan0
ps_ext7=usblan0
ps_ext8=usblan0
pswan=wan
pswan_mode=pdp
pswan_priority=1
RemoteManagement=0
rj45_plugstate_path=/sys/kernel/eth_debug/eth_state
rootdev_friendlyname=DEMO-UPnP
rootdev_manufacturer=DEMO
rootdev_modeldes=XXX
rootdev_modelname=XXX
os_url=http://www.demo.com
serialnumber=See-IMEI
static_dhcp_enable=1
static_ethwan_gw=
static_ethwan_ip=
static_ethwan_nm=
static_ethwan_pridns=
static_ethwan_secdns=
static_wifiwan_ipaddr=
static_wifiwan_netmask=
static_wifiwan_gateway=
wifiwan_pridns_manual=
wifiwan_secdns_manual=
static_wan_gateway=0.0.0.0
static_wan_ipaddr=0.0.0.0
static_wan_netmask=0.0.0.0
static_wan_primary_dns=0.0.0.0
static_wan_secondary_dns=0.0.0.0
swlanstr=sw0_lan
swvlan=sw0
swwanstr=sw0_wan
tc_downlink=
tc_uplink=
tc_local=1310720
tc_enable=0
time_limited=
time_to_2000_when_restore=yes
upnpEnabled=0
usblan=usblan0
WANPingFilter=0
websURLFilters=
wifiwan_priority=2
DDNS=
DDNS_Enable=0
DDNSAccount=
DDNSPassword=
DDNSProvider=
iccidPrevious=
imeiPrevious=
registerFlag=0
registeredRound=
secsEveryRound=1
secsEveryTime=1
regver=4.0
meid=
uetype=1
LocalDomain=m.home
data_volume_alert_percent=
data_volume_limit_size=
data_volume_limit_switch=0
data_volume_limit_unit=0
flux_day_total=0
flux_last_day=
flux_last_month=
flux_last_year=
flux_month_total=0
flux_set_day=
flux_set_month=
flux_set_year=
monthly_rx_bytes=0
monthly_time=0
monthly_tx_bytes=0
MonthlyConTime_Last=
dm_nextpollingtime=
fota_allowRoamingUpdate=0
fota_dl_pkg_size=0
fota_update_flag=
fota_updateIntervalDay=15
fota_upgrade_result=
fota_version_delta_id=
fota_version_delta_url=
fota_pkg_total_size=0
fota_version_file_size=
fota_version_md5sum=
fota_version_name=
fota_need_user_confirm_update=0
fota_need_user_confirm_download=1
fota_version_force_install=0
polling_nexttime=0
pwron_auto_check=1
fota_updateMode=1
fota_test_mode=0
fota_pkg_downloaded=0
fota_upgrade_result_internal=
mmi_battery_voltage_line=3348+3422+3450+3554+3577+3596+3617+3832+3633+3652+3679+3711+3761+3809+3860+3926+3980+4036+4153+4217+4296
mmi_fast_poweron=
####removed sleep_mode
#mmi_led_mode=sleep_mode
mmi_led_mode=
mmi_new_sms_blink_flag=1
mmi_show_pagetab=page1+page2+page3
mmi_showmode=led
#####for origin
#mmi_task_tab=net_task+ctrl_task+wifi_task+traffic_task+key_task+tip_task+tipwps_task+tipnetconnect_task+wificode_task+ssid_task+sms_task
#####for ufi
#mmi_task_tab=net_task+netsignal_task
####for mifi
mmi_task_tab=net_task+netsignal_task+wifi_task+battery_task+key_task+ctrl_task
mmi_temp_voltage_line=948+1199+1694+1736
mmi_use_protect=
mmi_use_wifi_usernum=1
leak_full_panic=
leak_list_max=
leak_set_flag=
monitor_period=300
netinf_flag=
skb_all_max=
skb_data_max=
skb_fromcp_max=
skb_max_fail=
skb_max_panic=
skb_size_max=
skb_tocp_max=
sntp_default_ip=***************;**************;**************;*************;*************;ntp.gwadar.cn;ntp-sz.chl.la;dns.sjtu.edu.cn;news.neu.edu.cn;dns1.synet.edu.cn;time-nw.nist.gov;pool.ntp.org;europe.pool.ntp.org
sntp_dst_enable=0
sntp_other_server0=
sntp_other_server1=
sntp_other_server2=
sntp_server0=time-nw.nist.gov
sntp_server1=pool.ntp.org
sntp_server2=europe.pool.ntp.org
sntp_sync_select_interval_time=30
sntp_time_set_mode=auto
sntp_timezone=CST-8
sntp_timezone_index=0
assert_errno=
comm_logsize=16384
cr_inner_version=V1.0.0B08
cr_version=UZ901LD1.0_KSBC_ZHONGCHUANG_SL_AUTH_IPv4v6_101_V2.01.01.02P42U28_14-250918
hw_version=UZ901_V1.6
TURNOFF_CHR_NUM=
watchdog_app=0
HTTP_SHARE_FILE=
HTTP_SHARE_STATUS=
HTTP_SHARE_WR_AUTH=readWrite
ipv6_pdp_type=IPv4v6
ipv6_wan_apn=
Language=zh-cn
manual_time_day=
manual_time_hour=
manual_time_minute=
manual_time_month=
manual_time_second=
manual_time_year=
sdcard_mode_option=0
AccessControlList0=
AccessPolicy0=0
ACL_mode=0
AuthMode=WPA2PSK
Channel=0
wifi_acs_num=8
closeEnable=0
closeTime=
CountryCode=CN
DefaultKeyID=0
DtimPeriod=1
EncrypType=AES
EX_APLIST=
EX_APLIST1=
EX_AuthMode=
EX_DefaultKeyID=
EX_EncrypType=
EX_mac=
EX_SSID1=Ufi-
EX_WEPKEY=
EX_wifi_profile=
EX_WPAPSK1=
FragThreshold=2346
HideSSID=0
HT_GI=1
Key1Str1=12345
Key2Str1=
Key3Str1=
Key4Str1=
Key1Type=1
Key2Type=
Key3Type=
Key4Type=
m_AuthMode=WPA2PSK
m_DefaultKeyID=
m_EncrypType=AES
m_HideSSID=0
m_Key1Str1=1234
m_Key2Str1=
m_Key3Str1=
m_Key4Str1=
m_Key1Type=1
m_Key2Type=
m_Key3Type=
m_Key4Type=
m_MAX_Access_num=0
m_NoForwarding=
m_show_qrcode_flag=0
m_SSID=Ufi_
m_ssid_enable=0
m_wapiType=
m_wifi_mac=901D45692A5C
m_WPAPSK1_aes=
m_WPAPSK1_encode=MTIzNDU2Nzg=
MAX_Access_num=10
MAX_Access_num_bak=10
NoForwarding=0
openEnable=0
openTime=
operater_ap=
RekeyInterval=3600
RTSThreshold=2347
show_qrcode_flag=0
Sleep_interval=-1
ssid_write_flag=0
SSID1=UFI-
tsw_sleep_time_hour=
tsw_sleep_time_min=
tsw_wake_time_hour=
tsw_wake_time_min=
wapiType=
wifi_force_40m=1
wifi_11n_cap=1
wifi_band=b
wifi_coverage=long_mode
wifi_hostname_black_list=
wifi_hostname_white_list=
wifi_mac=901D45692A5B
wifi_mac_black_list=
wifi_mac_white_list=
wifi_profile=
wifi_profile1=
wifi_profile2=
wifi_profile3=
wifi_profile4=
wifi_profile5=
wifi_profile6=
wifi_profile7=
wifi_profile8=
wifi_profile9=
wifi_profile_num=0
wifi_root_dir=
wifi_sta_connection=0
wifi_wps_index=1
wifiEnabled=1
wifilan=wlan0-va0
wifilan2=wlan0-va1
WirelessMode=6
WPAPSK1_aes=
WPAPSK1=1234567890
WPAPSK1_encode=
wps_mode=
WPS_SSID=
WscModeOption=0
monitor_apps=
at_netdog=
autorspchannel_list=all
soctime_switch=0
uart_control=0
uart_ctstrs_enable=
special_cmd_list=$MYNETREAD
##为入网入库芯片认证版本添加 begin
atcmd_stream1=AT+ZSET="w_instrument",1
atcmd_stream2=AT^SYSCONFIG=24,0,1,2
atcmd_stream3=AT+ZSET="csiiot",2
atcmd_stream4=AT+ZSET="dlparaflg",0
atcmd_stream5=AT+ZSET="MTNET_TEST",1;AT+ZGAAT=0;AT+ZSET="CMCC_TEST",1;AT+ZSET="LTE_INFO",6348;AT+ZSET="VOICE_SUPPORT",1;AT+ZSET="FDD_RELEASE",7;AT+ZSET="LTE_RELEASE",1;AT+ZSET="UE_PS_RELEASE",5;AT+ZSET="QOS_RELEASE",4;AT+ZSET="TEBS_THRESHOLD",0
atcmd_stream6=AT+ZSET="MTNET_TEST",1;AT+ZGAAT=0;AT+ZSET="LTE_INFO",6348;AT+ZSET="VOICE_SUPPORT",1;AT+ZSET="FDD_RELEASE",7;AT+ZSET="LTE_RELEASE",1;AT+ZSET="UE_PS_RELEASE",5;AT+ZSET="QOS_RELEASE",4;AT+ZSET="TEBS_THRESHOLD",0;AT+ZSET="IGNORE_SECURITY_SUPPORT",0;AT+ZSET="csifilter",0;AT+ZSET="csrhobandflg",0;AT+ZSET="dlparaflg",1;AT+ZSET="csiup",1;AT+ZSET="rfparaflag",0,0,1,0;AT+ZSET="csiiot",1;AT+ZSET="EXCEPT_RESET",0;AT+ZSET="ISIM_SUPPORT",1;AT+ZIMSTEST="MTNET_TEST",1;AT+ZSET="MANUAL_SEARCH",0
##为入网入库芯片认证版本添加 end
#for audio ctrl 
audio_priority=0123
customer_type=sdk_min
debug_mode=
cpIndCmdList=+ZMMI+ZURDY+ZUSLOT+ZICCID^MODE+ZPBIC+ZMSRI+CREG+CEREG+CGREG+CGEV
zephyr_filter_ip=
wait_timeout=2
sntp_sync_time=1
sntp_static_server0=time-nw.nist.gov
sntp_static_server1=pool.ntp.org
sntp_static_server2=europe.pool.ntp.org
vsim_bin_path=/mnt/userdata/vSim.bin
webv6_enable=
lan_ipv6addr=fe80::1
sms_vp=
at_select_timeout=
mtnet_test_mcc=
at_atv=
at_atq=
at_at_d=
base_ip_on_mac=0
quick_dial=1
xlat_enable=1
# qrzl
selected_net_band=0
##RSIM ESIM switch ctrl
#value: SIM_auto RSIM_only ESIM2_only ESIM1_only
sim_select=ESIM1_only
prev_sim_select=RSIM_only
#value: RSIM_ESIM2_pre ESIM2_RSIM_pre
sim_pre_mode=RSIM_ESIM2_pre
IccidLockEnable=0
effective_field_num=10
BeginIccid0=89860000000000000000
EndIccid0=89870000000000000000
BeginIccid1=89880000000000000000
EndIccid1=89880000000000000000
BeginIccid2=89880000000000000000
EndIccid2=89880000000000000000
BeginIccid3=89880000000000000000
EndIccid3=89880000000000000000
BeginIccid4=89880000000000000000
EndIccid4=89880000000000000000
BeginIccid5=89880000000000000000
EndIccid5=89880000000000000000
BeginIccid6=89880000000000000000
EndIccid6=89880000000000000000
BeginIccid7=89880000000000000000
EndIccid7=89880000000000000000
BeginIccid8=89880000000000000000
EndIccid8=89880000000000000000
BeginIccid9=89880000000000000000
EndIccid9=89880000000000000000
##sim switch auth
## 0: not password  1: Fixed passsword 2: algorithm password 3: only rsim fixed password
sim_auth_mode=0
sim_password=159258
