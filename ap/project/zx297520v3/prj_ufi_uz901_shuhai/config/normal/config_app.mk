
zte_app := busybox

ifeq ($(USE_FOTA),yes)
zte_app += busybox_recovery fota_upi
endif

zte_app += fs_check zte_log_agent\
			dnsmasq \
			Script \
			zte_amt \
			zte_webui_min clatd

#yes only when klocwork static analysis 
ifneq ($(KW_SKIP),yes)
zte_app += iproute2 iptables
endif

ifneq ($(CONFIG_WIFI_FUNCTION)), )
ifeq ($(findstring sta,$(CONFIG_WIFI_FUNCTION)), sta)
ifneq ($(KW_SKIP),yes)
ifneq ($(MK_SDK_VERSION),yes)
ifeq ($(CONFIG_WIFI_MODULE), ssv6x5x)
#zte_app += wpa_supplicant-2.6
endif
ifeq ($(CONFIG_WIFI_MODULE), aic8800)
zte_app += wpa_supplicant-2.10
endif
endif
endif
endif

ifeq ($(findstring ap,$(CONFIG_WIFI_FUNCTION)), ap)

ifeq ($(CONFIG_WIFI_MODULE), xr819)
ifneq ($(KW_SKIP),yes)
ifneq ($(MK_SDK_VERSION),yes)
zte_app += hostapd-2.6
endif
endif
endif

ifeq ($(CONFIG_WIFI_MODULE), ssv6x5x)
ifneq ($(KW_SKIP),yes)
ifneq ($(MK_SDK_VERSION),yes)
zte_app += hostapd-2.6
endif
endif
endif

ifeq ($(CONFIG_WIFI_MODULE), aic8800)
ifneq ($(KW_SKIP),yes)
ifneq ($(MK_SDK_VERSION),yes)
zte_app += hostapd-2.10
endif
endif
endif

endif

#zte_app += wlan
endif

ifeq ($(CONFIG_USE_WEBUI),yes)
zte_app += goahead
endif
zte_app += zte_ufi
ifneq ($(MK_SDK_VERSION),yes)
zte_app += cfg_tool zte_arp_proxy zte_ipv6_addr_conver zte_ipv6_slaac zte_ndp
ifneq ($(KW_SKIP),yes)
zte_app += adb dhcp6 radvd-2.14
endif
endif

ifeq ($(ENABLE_QRZL_APP),yes)
zte_app += qrzl_app
endif

#for app/zte_mainctrl&Script
export CONFIG_USER_ZTE_APP=y

#for app/iptables
export CONFIG_USER_IPTABLES_IPTABLES=y
export CONFIG_USER_IPTABLES_IP6TABLES=y

#for app/iproute2
export CONFIG_USER_IPROUTE2_TC_TC=y
export CONFIG_USER_IPROUTE2_IP_RTACCT=y
export CONFIG_USER_IPROUTE2_IP_IFCFG=y
export CONFIG_USER_IPROUTE2_IP_IP=y
export CONFIG_USER_IPROUTE2_IP_ROUTEF=y
export CONFIG_USER_IPROUTE2_IP_ROUTEL=y
export CONFIG_USER_IPROUTE2_IP_RTMON=y

export CONFIG_USER_FOTA_OPEN_SOURCE=n
