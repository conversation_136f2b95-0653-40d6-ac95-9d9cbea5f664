
zte_lib := libsoftap \
libatutils libatext libcpnv libamt

ifeq ($(USE_FOTA),yes)
zte_lib += libzte_dmapp
endif

ifeq ($(ENABLE_QRZL_APP),yes)
zte_lib += libwolfssl libcurl libpahomqttc
endif

#yes only  when klocwork static analysis 
ifneq ($(KW_SKIP),yes)
zte_lib += libsqlite libssl libnl libcurl

ifeq ($(USE_FOTA),yes)
zte_lib += liblzma
endif

endif

ifneq ($(CONFIG_WIFI_MODULE),)
zte_lib += libwlan_interface
endif

ifneq ($(MK_SDK_VERSION),yes)
zte_lib += libsoft_timer libnvram libzte_pbm

ifeq ($(USE_FOTA),yes)
zte_lib += libdmgr libupi
endif
endif

ifeq ($(USE_MIXDATA_SUPPORT),yes)
zte_lib += libaudiomix
endif
