#!/bin/bash

set -e

########配置开始##########
#输出镜像文件名
OUT_IMG_NAME=ap_udisk.img

#nand page size 2048Byte，erase  block size 128K, sub page 512Byte
UBI_ARGS="-m 2048 -p 0x20000 -s 512"

#U盘容量大小，比分区小10MB
UDISK_SIZE=50
########配置结束##########

if [ ! -d ./input ]; then
    echo "[error] 创建input目录，把要打包的文件放到input目录"
    exit 1
fi

OUT_IMG_NAME_FAT=${OUT_IMG_NAME}.fat
rm -f  $OUT_IMG_NAME  ${OUT_IMG_NAME}.fat
dd if=/dev/zero of=$OUT_IMG_NAME_FAT bs=1M count=$UDISK_SIZE
mkfs.vfat -F 32  $OUT_IMG_NAME_FAT

mkdir -p  ./root
mount -o loop $OUT_IMG_NAME_FAT   ./root
cp  -r ./input/*  ./root/
umount  ./root

echo ./ubinize-static.sh  vol_udisk $OUT_IMG_NAME $OUT_IMG_NAME_FAT  "$UBI_ARGS"
./ubinize-static.sh  vol_udisk $OUT_IMG_NAME $OUT_IMG_NAME_FAT  "$UBI_ARGS"

rm -f ${OUT_IMG_NAME}.fat

