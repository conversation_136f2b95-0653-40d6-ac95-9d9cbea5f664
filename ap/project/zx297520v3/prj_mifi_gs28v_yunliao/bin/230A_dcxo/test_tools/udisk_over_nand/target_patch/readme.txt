
以prj_mifi为例
1、修改ap/project/zx297520v3/prj_mifi/config/normal/config.busybox，打开下面选项

CONFIG_MKFS_VFAT
CONFIG_UBIATTACH
CONFIG_UBIDETACH
CONFIG_UBIMKVOL
CONFIG_UBIRMVOL
CONFIG_UBIRSVOL
CONFIG_UBIUPDATEVOL

修改ap/project/zx297520v3/prj_mifi/config/normal/config.linux
CONFIG_MTD_UBI_BLOCK=y
CONFIG_MTD_UBI_BLOCK_CACHED=y
CONFIG_MTD_UBI_BLOCK_WRITE_SUPPORT=y
CONFIG_MSDOS_FS=y
CONFIG_NLS_DEFAULT="936"
CONFIG_NLS_CODEPAGE_437=y

2、ubiblock、ubiformat程序，拷贝到预置文件系统里，并且保证有可执行权限。
以prj_mifi工程为例
cp  ubiblock ap/project/zx297520v3/prj_mifi/fs/normal/rootfs/sbin/ubiblock
chmod a+x ap/project/zx297520v3/prj_mifi/fs/normal/rootfs/sbin/ubiblock
cp  ubiformat ap/project/zx297520v3/prj_mifi/fs/normal/rootfs/sbin/ubiformat
chmod a+x ap/project/zx297520v3/prj_mifi/fs/normal/rootfs/sbin/ubiformat


3、配置udisk.sh
cp udisk.sh  ap/project/zx297520v3/prj_mifi/fs/normal/rootfs/etc/

修改ubiattach  /dev/ubi_ctrl  -m  8，其中8是u盘mtd分区编号，修改成实际的mtd分区。
修改ubiformat /dev/mtd8 -y -s 512， 其中/dev/mtd8
修改ubimkvol  /dev/ubi0  -N  vol_udisk -t dynamic  -s  52428800，其中52428800修改为实际分区大小减去10MB，再换成字节。
比如分区300MB，290*1024*1024=304087040
可以参考 /sys/devices/virtual/ubi/ubi0/ubi0_0/data_bytes 里值

修改/etc/rc,在insmod /lib/cpko/cpko.ko下面一行增加
sh  /etc/udisk.sh &


4、修改ap/project/zx297520v3/prj_mifi/fs/normal/rootfs/etc_ro/default/default_parameter_sys
usb_tcard_lun_path=/dev/ubiblock0_0
