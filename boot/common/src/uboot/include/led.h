/*******************************************************************************
 * Copyright (C) 2016, ZIXC Corporation.
 *
 * File Name:	drvs_led.h
 * File Mark:
 * Description:
 * Others:
 * Version:       V1.0
 * Author:        yuxiang
 * Date:          2014-07-07
 * History 1:
 *     Date:
 *     Version:
 *     Author:
 *     Modification:
 * History 2:
  ********************************************************************************/

#ifndef _DRVS_LED_H
#define _DRVS_LED_H
#include "board.h"

/****************************************************************************
* 	                                        Include files
****************************************************************************/

/****************************************************************************
* 	                                        Macros
****************************************************************************/

#include <linux/types.h>


/*00h*/

/*03h*/

/****************************************************************************
* 	                                        Types
****************************************************************************/

typedef enum
{
    /* 0 ~ -29: commom */
    DRV_SUCCESS                                     = 0,        /* successed */
    DRV_ERROR                                        = -1,      /* failed */
    DRV_ERR_INVALID_IOCTL_CMD = -2,      /* no this control command branch */
    DRV_ERR_NOT_SUPPORTED          = -3,      /* this function hasn't been supported */
    DRV_ERR_INVALID_PARAM          = -4,      /* the input parameter is invalid */
    DRV_ERR_MEM_ALLOC                  = -5,      /* failed to malloc memory */
    DRV_ERR_HISR_CREATE_FAIL     = -6,      /* failed to create hisr */
    DRV_ERR_TIMEOUT                         = -7,      /* timeout for a block waitting operation */
    DRV_ERR_BUSY                                 = -8,      /* busy now to do the request operation */
    DRV_ERR_NOT_OPENED                 = -9,      /* the device to operate hasn't been opened yet */
    DRV_ERR_OPEN_TIMES                  = -10,    /* try to open a device which has been opened already */
    DRV_ERR_NOT_STARTED               = -11,    /* the device to operate hasn't been started yet */
    DRV_ERR_START_TIMES                = -12,    /* try to open a device which has been opened already */
    /* reserved */

}T_DRVS_RETURN_TYPE;

/****************************************************************************
* 	                                        Constants
****************************************************************************/

/****************************************************************************
* 	                                        Global  Variables
****************************************************************************/

/****************************************************************************
* 	                                        Function Prototypes
****************************************************************************/

typedef signed char SINT8;
typedef unsigned char UINT8;

typedef signed short SINT16;
typedef unsigned short UINT16;

typedef signed int SINT32;
typedef unsigned int UINT32;

#if defined(CONFIG_ZX297520V3E_MIFI_MINI_XR819)
#define LED_WIFI_BLUE_GPIO		GPIO39       //real wifi green led
#define LED_SMS_BLUE_GPIO		GPIO40          //real sms green led
#define LED_BAT_RED_GPIO		GPIO41          
#define LED_BAT_GREEN_GPIO		GPIO42          
#define LED_MODEM_RED_GPIO		GPIO124
#define LED_MODEM_BLUE_GPIO		GPIO125			//real modem green
#define LED_WIFI_BLUE_GPIO_FUN		GPIO39_GPIO39
#define LED_SMS_BLUE_GPIO_FUN		GPIO40_GPIO40
#define LED_BAT_RED_GPIO_FUN		GPIO41_GPIO41
#define LED_BAT_GREEN_GPIO_FUN		GPIO42_GPIO42
#define LED_MODEM_RED_GPIO_FUN		GPIO124_GPIO124
#define LED_MODEM_BLUE_GPIO_FUN		GPIO125_GPIO125

#elif defined(CONFIG_ZX297520V3E_CPE_SWITCH)
#define LED_LTE_RED			GPIO45
#define LED_LTE_BLUE		GPIO46
#define LED_WIFI 		 	GPIO86
#define LED_WPS 		 	GPIO72
#define LED_RJ11 			GPIO22
#define LED_4G_1 			GPIO29
#define LED_4G_2 			GPIO30
#define LED_4G_3 			GPIO73
#define LED_4G_4 			GPIO74
#define LED_4G_5 			GPIO75

#define LED_POWER_FUNC_SEL 		GPIO21_GPIO21
#define LED_LTE_RED_FUNC_SEL 	GPIO45_GPIO45
#define LED_LTE_BLUE_FUNC_SEL 	GPIO46_GPIO46
#define LED_WIFI_FUNC_SEL 		GPIO86_GPIO86
#define LED_WPS_FUNC_SEL		GPIO72_GPIO72
#define LED_RJ11_FUNC_SEL		GPIO22_GPIO22
#define LED_4G_1_FUNC_SEL		GPIO29_GPIO29
#define LED_4G_2_FUNC_SEL		GPIO30_GPIO30
#define LED_4G_3_FUNC_SEL		GPIO73_GPIO73
#define LED_4G_4_FUNC_SEL		GPIO74_GPIO74
#define LED_4G_5_FUNC_SEL		GPIO75_GPIO75

#elif defined(CONFIG_JCV_HW_MZ801_V1_2)
#define LED_MODEM_RED_GPIO		GPIO124
#define LED_MODEM_GREEN_GPIO	GPIO125		
#define LED_BAT_RED_GPIO		GPIO41          
#define LED_BAT_GREEN_GPIO		GPIO42
#define LED_WIFI_GPIO		    GPIO39 
#define LED_WIFI_GREEN_GPIO		GPIO40  

#define LED_MODEM_RED_GPIO_FUN		GPIO124_GPIO124
#define LED_MODEM_BLUE_GPIO_FUN		GPIO125_GPIO125
#define LED_BAT_RED_GPIO_FUN		GPIO41_GPIO41
#define LED_BAT_GREEN_GPIO_FUN		GPIO42_GPIO42
#define LED_WIFI_GPIO_FUN		    GPIO39_GPIO39
#define LED_WIFI_GREEN_GPIO_FUN		GPIO40_GPIO40

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_POWERBANK_DZ801)
#define VCC_3V3_EN_GPIO  GPIO130
#define LED_MODEM_RED_GPIO  GPIO124
#define LED_MODEM_GREEN_GPIO  GPIO125
#define LED_MODEM_BLUE_GPIO  GPIO123
#define LED_WIFI_BLUE_GPIO GPIO39

//use for led on battery led (IP5306H) 
#define KEY_5306_DIS_GPIO GPIO121
#define KEY_5306_GPIO     GPIO122

#define VCC_3V3_EN_GPIO_FUN         GPIO130_GPIO130
#define LED_MODEM_RED_GPIO_FUN      GPIO124_GPIO124
#define LED_MODEM_GREEN_GPIO_FUN    GPIO125_GPIO125
#define LED_MODEM_BLUE_GPIO_FUN     GPIO123_GPIO123
#define LED_WIFI_BLUE_GPIO_FUN      GPIO39_GPIO39
#define KEY_5306_DIS_GPIO_FUN       GPIO121_GPIO121
#define KEY_5306_GPIO_FUN           GPIO122_GPIO122

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_GS28V_V1)
#define VCC_3V3_EN_GPIO  GPIO130
#define LED_MODEM_RED_GPIO  GPIO123
#define LED_MODEM_GREEN_GPIO  GPIO39

#define LED_DIGITS_PIN_1_GPIO GPIO60
#define LED_DIGITS_PIN_2_GPIO GPIO61
#define LED_DIGITS_PIN_3_GPIO GPIO62
#define LED_DIGITS_PIN_4_GPIO GPIO63
#define LED_DIGITS_PIN_5_GPIO GPIO64


#define VCC_3V3_EN_GPIO_FUN         GPIO130_GPIO130
#define LED_MODEM_RED_GPIO_FUN      GPIO123_GPIO123
#define LED_MODEM_GREEN_GPIO_FUN    GPIO39_GPIO39

#define LED_DIGITS_PIN_1_GPIO_FUN       GPIO60_GPIO60
#define LED_DIGITS_PIN_2_GPIO_FUN       GPIO61_GPIO61
#define LED_DIGITS_PIN_3_GPIO_FUN       GPIO62_GPIO62
#define LED_DIGITS_PIN_4_GPIO_FUN       GPIO63_GPIO63
#define LED_DIGITS_PIN_5_GPIO_FUN       GPIO64_GPIO64


#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_MZ901_V1_0)
#define VCC_3V3_EN_GPIO  GPIO130
#define LED_MODEM_RED_GPIO  GPIO63
#define LED_MODEM_GREEN_GPIO  GPIO64
#define LED_WIFI_BLUE_GPIO GPIO55
#define LED_BAT_PERCENT_25_GPIO GPIO59
#define LED_BAT_PERCENT_50_GPIO GPIO60
#define LED_BAT_PERCENT_75_GPIO GPIO61
#define LED_BAT_PERCENT_100_GPIO GPIO62

#define VCC_3V3_EN_GPIO_FUN         GPIO130_GPIO130
#define LED_MODEM_RED_GPIO_FUN      GPIO63_GPIO63
#define LED_MODEM_GREEN_GPIO_FUN    GPIO64_GPIO64
#define LED_WIFI_BLUE_GPIO_FUN      GPIO55_GPIO55
#define LED_BAT_PERCENT_25_GPIO_FUN       GPIO59_GPIO59
#define LED_BAT_PERCENT_50_GPIO_FUN       GPIO60_GPIO60
#define LED_BAT_PERCENT_75_GPIO_FUN       GPIO61_GPIO61
#define LED_BAT_PERCENT_100_GPIO_FUN       GPIO62_GPIO62

#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
#define LED_WIFI_BLUE_GPIO GPIO65       //wifi blue led
#define LED_SMS_BLUE_GPIO  GPIO40          //sms blue led
#define LED_BAT_RED_GPIO  GPIO63          //bat red led
#define LED_BAT_GREEN_GPIO  GPIO62          //bat_green led
#define LED_MODEM_RED_GPIO  GPIO61
#define LED_MODEM_BLUE_GPIO  GPIO60
#define LED_WIFI_BLUE_GPIO_FUN GPIO65_GPIO65//wifi blue led
#define LED_SMS_BLUE_GPIO_FUN  GPIO40_GPIO40//sms blue led
#define LED_BAT_RED_GPIO_FUN  GPIO63_GPIO63//bat red led change to green
#define LED_BAT_GREEN_GPIO_FUN  GPIO62_GPIO62//bat_green led
#define LED_MODEM_RED_GPIO_FUN  GPIO61_GPIO61
#define LED_MODEM_BLUE_GPIO_FUN  GPIO60_GPIO60

#elif defined(CONFIG_JCV_HW_UZ901_V1_4)

#define LED_MODEM_RED_GPIO  GPIO124
#define LED_MODEM_GREEN_GPIO  GPIO125
#define LED_WIFI_BLUE_GPIO  GPIO123

#define LED_MODEM_RED_GPIO_FUN      GPIO124_GPIO124
#define LED_MODEM_GREEN_GPIO_FUN    GPIO125_GPIO125
#define LED_WIFI_BLUE_GPIO_FUN     GPIO123_GPIO123

#else
#define LED_WIFI_BLUE_GPIO GPIO39       //wifi blue led
#define LED_SMS_BLUE_GPIO  GPIO40          //sms blue led
#define LED_BAT_RED_GPIO  GPIO42          //bat red led
#define LED_BAT_GREEN_GPIO  GPIO41          //bat_green led
#define LED_MODEM_RED_GPIO  GPIO124
#define LED_MODEM_BLUE_GPIO  GPIO123
#define LED_WIFI_BLUE_GPIO_FUN GPIO39_GPIO39//wifi blue led
#define LED_SMS_BLUE_GPIO_FUN  GPIO40_GPIO40//sms blue led
#define LED_BAT_RED_GPIO_FUN  GPIO42_GPIO42//bat red led change to green
#define LED_BAT_GREEN_GPIO_FUN  GPIO41_GPIO41//bat_green led
#define LED_MODEM_RED_GPIO_FUN  GPIO124_GPIO124
#define LED_MODEM_BLUE_GPIO_FUN  GPIO123_GPIO123
#endif

#define LED_BITFVAL(var, lsh)   ( (var) << (lsh) )
#define LED_BITFMASK(wid, lsh)  ( ((1U << (wid)) - 1) << (lsh) )
#define LED_BITFEXT(var, wid, lsh)   ((var & LED_BITFMASK(wid, lsh)) >> (lsh))

/* adc & sink and sink current select	*/
#if 0/* adc & sink and sink current select	*/
#define ZX234290_REG_ADDR_SYS_CTRL        	0x07
#define ZX234290_REG_ADDR_SINK2_CUR_SEL    	0x08
#define ZX234290_REG_ADDR_SINK1_CUR_SEL   	0x09
#endif
typedef enum led_channel {
    LED_WIFI_BLUE =0x0,       //wifi green led
    LED_SMS_BLUE,            //sms green led
    LED_BAT_RED,            //bat red led
    LED_BAT_GREEN,            //bat_green led
    LED_MODEM_BLUE,            //modem green led
    LED_MODEM_GREEN,            //no
    LED_MODEM_RED,            //modem red
    LED_CHANNEL_MAX,
}led_channel;

typedef enum led_current {
    LED_CURRENT_0 =0x0,
    LED_CURRENT_5,
    LED_CURRENT_10,
    LED_CURRENT_15,
    LED_CURRENT_20,
    LED_CURRENT_25,
    LED_CURRENT_30,
    LED_CURRENT_35,
    LED_CURRENT_40,

    SM5201_CURRENT_MAX,
}led_current;

typedef enum led_status {
	LED_STATUS_OFF  = 0x0,
	LED_STATUS_ON   = 0x1,

	LED_STATUS_MAX,
}led_status;


/****************************************************************************
* 	                                        Constants
****************************************************************************/

/****************************************************************************
* 	                                        Global  Variables
****************************************************************************/

/****************************************************************************
* 	                                        Function Prototypes
****************************************************************************/

SINT32 led_SetStatus(led_channel channel, led_status status);
SINT32 led_SetBlink(led_channel channel,	UINT32 delay_on, UINT32 delay_off);
SINT32 led_SetLEDcurrent(UINT8 sink, led_current current);

void zx29_led_init(void);
void zx29_led_PowerOnLedOn(void);

void zx29_led_PowerOnLedOff(void);

#endif/*_DRVS_LED_H*/
