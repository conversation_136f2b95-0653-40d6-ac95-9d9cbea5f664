/*******************************************************************************
 * Copyright (C) 2016, ZIXC Corporation.
 *
 * File Name:   hal_led.c
 * File Mark:
 * Description:  This file contains the hal layer routines for i2c driver.
 * Others:
 * Version:       V1.0
 * Author:        yx
 * Date:          2014-07-03
 * History 1:
 *     Date:
 *     Version:
 *     Author:
 *     Modification:
 * History 2:
  ******************************************************************************/

/****************************************************************************
*                                  Include files
****************************************************************************/
#include <drvs_gpio.h>
#include <led.h>

/****************************************************************************
* 	                                           Local Types
****************************************************************************/

/****************************************************************************
* 	                                           Local Constants
****************************************************************************/

#if !defined(CONFIG_QRZL_UE) && !defined(CONFIG_JCV_HW_MZ803_V3_2) && !defined(CONFIG_JCV_HW_UZ901_V1_4) && !defined(CONFIG_JCV_HW_MZ801_V1_2) 
SINT32 led_SetStatus(led_channel channel, led_status status)
{
    SINT32 ret = 0;
    if (status == LED_STATUS_ON)
    {
        switch (channel)
        {
        case LED_WIFI_BLUE:
            zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_HIGH);
            break;
        case LED_SMS_BLUE:
            zDrvGpio_SetOutputValue(LED_SMS_BLUE_GPIO,GPIO_HIGH);
            break;
        case LED_BAT_RED:
            zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO,GPIO_HIGH);
            break;
        case LED_BAT_GREEN:
            zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO,GPIO_HIGH);
            break;
		case LED_MODEM_BLUE:
			zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO,GPIO_HIGH);			
			break;
		case LED_MODEM_RED:		
			zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO,GPIO_HIGH);
			break;		
        default:
            break;
			

        }
    }
    else if (status == LED_STATUS_OFF)
    {
        switch (channel)
        {
        case LED_WIFI_BLUE:
            zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_LOW);
            break;
        case LED_SMS_BLUE:
            zDrvGpio_SetOutputValue(LED_SMS_BLUE_GPIO,GPIO_LOW);
            break;
        case LED_BAT_RED:
            zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO,GPIO_LOW);
            break;
        case LED_BAT_GREEN:
            zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO,GPIO_LOW);
            break;
		case LED_MODEM_BLUE:
			zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO,GPIO_LOW);			
			break;
		case LED_MODEM_RED:		
			zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO,GPIO_LOW);
			break;		
        default:
            break;
			

        }
    }
    else
    {
        return -1;
    }

    return ret;

}
#endif

#if 0

/*******************************************************************************
  * Function:	       led_SetLEDcurrent
  * Description:
  * Parameters:
  *   Input:		 led_color:  	LED_RED / LED_GREEN / LED_BLUE
  *				brightness: LED_BRIGHTNESS_1 ~ LED_BRIGHTNESS_16
  *   Output:
  * Returns:					success or fail
  * Others: blue led can't set brightness, only can set on/off
  ********************************************************************************/
SINT32 led_SetLEDcurrent(UINT8 sink, led_current current)
{
    SINT32 ret = 0;
    UINT8 slv_addr=0, reg_addr=0, reg_val=0, mask=0;

    slv_addr = ZX234290_I2C_SLAVE_ADDR0;

    reg_addr = ZX234290_REG_ADDR_SYS_CTRL;
    if (current)
    {
        switch (sink)
        {
            case 1:
            reg_val = LED_BITFVAL(1, 0);	/* 0x07 寄存�? [1:0]	*/
            break;
            case 2:
            reg_val = LED_BITFVAL(1, 1);	/* 0x07 寄存�? [1:0]	*/
            break;
            default:
            break;
        }
    }
    else
    {
        switch (sink)
        {
            case 1:
                reg_val = LED_BITFVAL(0, 0);
                break;
            case 2:
                reg_val = LED_BITFVAL(0, 1);	/* 0x07 寄存�? [1:0]	*/
                break;
            default:
                break;
        }
    }

    switch (sink)
    {
        case 1:
            mask = LED_BITFMASK(1, 0);
            break;
        case 2:
            mask = LED_BITFMASK(1, 1);;	/* 0x07 寄存�? [1:0]	*/
            break;
        default:
            break;
    }


    ret = zx297520_led_SetRegister(reg_addr, reg_val, mask);
    //zOss_Sleep(500);

    switch (sink)
    {
        case 1:
            reg_addr = ZX234290_REG_ADDR_SINK1_CUR_SEL;
            break;
        case 2:
            reg_addr = ZX234290_REG_ADDR_SINK2_CUR_SEL;
            break;
        default:
            break;
    }

    reg_val = LED_BITFVAL(current, 0);	/* 0x07 寄存�? [1:0]	*/
    mask = LED_BITFMASK(4, 0);
    ret = zx297520_led_SetRegister(reg_addr, reg_val, mask);

    if (ret != 0)
    {
        return -1;
    }

    return 0;
}
#endif
void zx29_led_init(void)
{
#if defined(CONFIG_JCV_HW_MZ801_V1_2)
    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);	

    zDrvGpio_SetFunc(LED_MODEM_GREEN_GPIO, LED_MODEM_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_GREEN_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_BAT_RED_GPIO,LED_BAT_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_RED_GPIO,GPIO_OUT);
	
    zDrvGpio_SetFunc(LED_BAT_GREEN_GPIO,LED_BAT_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_GREEN_GPIO,GPIO_OUT);
	
    zDrvGpio_SetFunc(LED_WIFI_GPIO, LED_WIFI_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_GPIO, GPIO_OUT);	
	
    zDrvGpio_SetFunc(LED_WIFI_GREEN_GPIO, LED_WIFI_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_GREEN_GPIO, GPIO_OUT);	

    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_GREEN_GPIO, GPIO_HIGH);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_POWERBANK_DZ801)
    zDrvGpio_SetFunc(VCC_3V3_EN_GPIO, VCC_3V3_EN_GPIO_FUN);
    zDrvGpio_SetDirection(VCC_3V3_EN_GPIO, GPIO_OUT);	

    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);

    zDrvGpio_SetFunc(LED_MODEM_GREEN_GPIO, LED_MODEM_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_GREEN_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_MODEM_BLUE_GPIO, LED_MODEM_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_BLUE_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_WIFI_BLUE_GPIO, LED_WIFI_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_BLUE_GPIO, GPIO_OUT);

    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    //blue led off 因为后面app 没有使用
    zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_HIGH);

#if 0
#define UDELAY_1SEC	(100000/3)
    //disable KEY_5306_DIS
    zDrvGpio_SetFunc(KEY_5306_DIS_GPIO, KEY_5306_DIS_GPIO_FUN);
    zDrvGpio_SetDirection(KEY_5306_DIS_GPIO, GPIO_OUT);	

    zDrvGpio_SetFunc(KEY_5306_GPIO, KEY_5306_GPIO_FUN);
    zDrvGpio_SetDirection(KEY_5306_GPIO, GPIO_OUT);	

    //操作 KEY_5306_GPIO 模拟一次短按间 点亮电池 電亮�?
    zDrvGpio_SetOutputValue(KEY_5306_DIS_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(KEY_5306_GPIO, GPIO_HIGH);

    udelay(UDELAY_1SEC * 2);
    zDrvGpio_SetOutputValue(KEY_5306_DIS_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(KEY_5306_GPIO, GPIO_LOW);
#endif

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_GS28V_V1)
    //GS28V LED 初始化 和 数码管GPIO 初始化
    zDrvGpio_SetFunc(VCC_3V3_EN_GPIO, VCC_3V3_EN_GPIO_FUN);
    zDrvGpio_SetDirection(VCC_3V3_EN_GPIO, GPIO_OUT);	

    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);

    zDrvGpio_SetFunc(LED_MODEM_GREEN_GPIO, LED_MODEM_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_GREEN_GPIO, GPIO_OUT);

    // 设置数码管5 PIN 为高阻态
    zDrvGpio_SetFunc(LED_DIGITS_PIN_1_GPIO, LED_DIGITS_PIN_1_GPIO_FUN);
    zDrvGpio_SetDirection(LED_DIGITS_PIN_1_GPIO, GPIO_IN);
    zDrvGpio_SetFunc(LED_DIGITS_PIN_2_GPIO, LED_DIGITS_PIN_2_GPIO_FUN);
    zDrvGpio_SetDirection(LED_DIGITS_PIN_2_GPIO, GPIO_IN);
    zDrvGpio_SetFunc(LED_DIGITS_PIN_3_GPIO, LED_DIGITS_PIN_3_GPIO_FUN);
    zDrvGpio_SetDirection(LED_DIGITS_PIN_3_GPIO, GPIO_IN);
    zDrvGpio_SetFunc(LED_DIGITS_PIN_4_GPIO, LED_DIGITS_PIN_4_GPIO_FUN);
    zDrvGpio_SetDirection(LED_DIGITS_PIN_4_GPIO, GPIO_IN);
    zDrvGpio_SetFunc(LED_DIGITS_PIN_5_GPIO, LED_DIGITS_PIN_5_GPIO_FUN);
    zDrvGpio_SetDirection(LED_DIGITS_PIN_5_GPIO, GPIO_IN);

    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    // zDrvGpio_SetOutputValue(LED_DIGITS_PIN_1_GPIO, GPIO_LOW);
    // zDrvGpio_SetOutputValue(LED_DIGITS_PIN_2_GPIO, GPIO_LOW);
    // zDrvGpio_SetOutputValue(LED_DIGITS_PIN_3_GPIO, GPIO_LOW);
    // zDrvGpio_SetOutputValue(LED_DIGITS_PIN_4_GPIO, GPIO_LOW);
    // zDrvGpio_SetOutputValue(LED_DIGITS_PIN_5_GPIO, GPIO_LOW);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_MZ901_V1_0)
    //MZ901 led �?�?反的�? 低电平亮
    zDrvGpio_SetFunc(VCC_3V3_EN_GPIO, VCC_3V3_EN_GPIO_FUN);
    zDrvGpio_SetDirection(VCC_3V3_EN_GPIO, GPIO_OUT);	

    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);

    zDrvGpio_SetFunc(LED_MODEM_GREEN_GPIO, LED_MODEM_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_GREEN_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_WIFI_BLUE_GPIO, LED_WIFI_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_BLUE_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_BAT_PERCENT_25_GPIO, LED_BAT_PERCENT_25_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_PERCENT_25_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_BAT_PERCENT_50_GPIO, LED_BAT_PERCENT_50_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_PERCENT_50_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_BAT_PERCENT_75_GPIO, LED_BAT_PERCENT_75_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_PERCENT_75_GPIO, GPIO_OUT);

    zDrvGpio_SetFunc(LED_BAT_PERCENT_100_GPIO, LED_BAT_PERCENT_100_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_PERCENT_100_GPIO, GPIO_OUT);

    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_25_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_50_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_75_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_100_GPIO, GPIO_LOW);
#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
    zDrvGpio_SetFunc(GPIO130,GPIO130_GPIO130);
    zDrvGpio_SetDirection(GPIO130,GPIO_OUT);	
    zDrvGpio_SetOutputValue(GPIO130,GPIO_HIGH);

    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO,GPIO_HIGH);

    zDrvGpio_SetFunc(LED_MODEM_BLUE_GPIO,LED_MODEM_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_BLUE_GPIO,GPIO_OUT);
    zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO,GPIO_HIGH);

    zDrvGpio_SetFunc(LED_BAT_RED_GPIO,LED_BAT_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_RED_GPIO,GPIO_OUT);
    zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO,GPIO_HIGH);

    zDrvGpio_SetFunc(LED_BAT_GREEN_GPIO,LED_BAT_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_GREEN_GPIO,GPIO_OUT);
    zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO,GPIO_HIGH);
	
    zDrvGpio_SetFunc(LED_WIFI_BLUE_GPIO,LED_WIFI_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_BLUE_GPIO,GPIO_OUT);	
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_HIGH);

	zDrvGpio_SetFunc(GPIO64,GPIO64_GPIO64);
    zDrvGpio_SetDirection(GPIO64,GPIO_OUT);	
    zDrvGpio_SetOutputValue(GPIO64,GPIO_HIGH);

#elif defined(CONFIG_JCV_HW_UZ901_V1_4)
    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO,GPIO_HIGH);

    zDrvGpio_SetFunc(LED_MODEM_GREEN_GPIO,LED_MODEM_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_GREEN_GPIO,GPIO_OUT);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO,GPIO_HIGH);
	
    zDrvGpio_SetFunc(LED_WIFI_BLUE_GPIO,LED_WIFI_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_BLUE_GPIO,GPIO_OUT);	
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_HIGH);

#else
    zDrvGpio_SetFunc(LED_WIFI_BLUE_GPIO,LED_WIFI_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_WIFI_BLUE_GPIO,GPIO_OUT);	
	zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_LOW);

    zDrvGpio_SetFunc(LED_SMS_BLUE_GPIO,LED_SMS_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_SMS_BLUE_GPIO,GPIO_OUT);
	zDrvGpio_SetOutputValue(LED_SMS_BLUE_GPIO,GPIO_LOW);

    zDrvGpio_SetFunc(LED_BAT_RED_GPIO,LED_BAT_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_RED_GPIO,GPIO_OUT);
	zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO,GPIO_LOW);

    zDrvGpio_SetFunc(LED_BAT_GREEN_GPIO,LED_BAT_GREEN_GPIO_FUN);
    zDrvGpio_SetDirection(LED_BAT_GREEN_GPIO,GPIO_OUT);
	zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO,GPIO_LOW);
	
    zDrvGpio_SetFunc(LED_MODEM_RED_GPIO,LED_MODEM_RED_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_RED_GPIO,GPIO_OUT);
	zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO,GPIO_LOW);

    zDrvGpio_SetFunc(LED_MODEM_BLUE_GPIO,LED_MODEM_BLUE_GPIO_FUN);
    zDrvGpio_SetDirection(LED_MODEM_BLUE_GPIO,GPIO_OUT);
	zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO,GPIO_LOW);
#endif
}

void zx29_led_PowerOnLedOn(void)
{
#if defined(CONFIG_JCV_HW_MZ801_V1_2)
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_GREEN_GPIO, GPIO_HIGH);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_POWERBANK_DZ801)
    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    //blue led off 因为后面app 没有使用
    zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_HIGH);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_GS28V_V1)
    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_MZ901_V1_0)
    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_25_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_50_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_75_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_100_GPIO, GPIO_LOW);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
    zDrvGpio_SetOutputValue(GPIO130,GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO,GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO,GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO,GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_HIGH);
    zDrvGpio_SetOutputValue(GPIO64,GPIO_HIGH);
#elif defined(CONFIG_JCV_HW_UZ901_V1_4)
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_HIGH);
#else
	#if 1
    led_SetStatus(LED_MODEM_RED,LED_STATUS_OFF);
    led_SetStatus(LED_BAT_GREEN,LED_STATUS_OFF);
	
    led_SetStatus(LED_WIFI_BLUE,LED_STATUS_ON);
    led_SetStatus(LED_SMS_BLUE,LED_STATUS_ON);
    led_SetStatus(LED_BAT_RED,LED_STATUS_ON);
    led_SetStatus(LED_MODEM_BLUE,LED_STATUS_ON);
	#endif
#endif
	
}
void zx29_led_PowerOnLedOff(void)
{	
#if defined(CONFIG_JCV_HW_MZ801_V1_2)
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_GREEN_GPIO, GPIO_LOW);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_POWERBANK_DZ801)
    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_LOW);

#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_GS28V_V1)
    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_LOW);
    
#elif defined(CONFIG_JCV_HW_MZ803_V3_2) && defined(CONFIG_JCV_HW_MZ901_V1_0)
    zDrvGpio_SetOutputValue(VCC_3V3_EN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_25_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_50_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_75_GPIO, GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_BAT_PERCENT_100_GPIO, GPIO_HIGH);
#elif defined(CONFIG_JCV_HW_MZ803_V3_2)
    zDrvGpio_SetOutputValue(GPIO130,GPIO_HIGH);
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_BLUE_GPIO,GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_RED_GPIO,GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_BAT_GREEN_GPIO,GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO,GPIO_LOW);
    zDrvGpio_SetOutputValue(GPIO64,GPIO_LOW);
#elif defined(CONFIG_JCV_HW_UZ901_V1_4)
    zDrvGpio_SetOutputValue(LED_MODEM_RED_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_MODEM_GREEN_GPIO, GPIO_LOW);
    zDrvGpio_SetOutputValue(LED_WIFI_BLUE_GPIO, GPIO_LOW);
#else
    udelay(19500);	/*300ms*/
    led_SetStatus(LED_WIFI_BLUE,LED_STATUS_OFF);
    led_SetStatus(LED_SMS_BLUE,LED_STATUS_OFF);
    led_SetStatus(LED_MODEM_BLUE,LED_STATUS_OFF);	
#endif
}

